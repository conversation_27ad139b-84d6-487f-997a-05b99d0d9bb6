# StockPal Architecture Documentation

## System Overview

StockPal is a modular stock analysis system built with Python that provides technical analysis, trend prediction, and trading signal generation. The architecture follows a layered approach with clear separation of concerns between data fetching, analysis, and presentation layers.

## High-Level Architecture

```
┌─────────────────────────────────────────────────────────────┐
│                    Presentation Layer                       │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────────────┐  │
│  │   Web UI    │  │  REST API   │  │   CLI Interface     │  │
│  │  (web_test) │  │   (Flask)   │  │   (Python CLI)     │  │
│  └─────────────┘  └─────────────┘  └─────────────────────┘  │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                    Business Logic Layer                     │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────┐  │
│  │ Analysis Service│  │ Signal Synthesis│  │ Backtesting │  │
│  │ (TrendPredictor)│  │   (Indicators)  │  │   Engine    │  │
│  └─────────────────┘  └─────────────────┘  └─────────────┘  │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                      Data Access Layer                      │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────────────┐  │
│  │ Data Scraper│  │ HTTP Service│  │   Cache Manager     │  │
│  │  (Factory)  │  │  (Requests) │  │   (File System)    │  │
│  └─────────────┘  └─────────────┘  └─────────────────────┘  │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                     External Data Sources                   │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────────────┐  │
│  │     SSI     │  │  VietStock  │  │       CafeF         │  │
│  │   (API)     │  │    (API)    │  │      (API)          │  │
│  └─────────────┘  └─────────────┘  └─────────────────────┘  │
└─────────────────────────────────────────────────────────────┘
```

## Core Components

### 1. Data Access Layer

#### DataScraper (Factory Pattern)
- **Location**: `server/stockpal/data/data_scraper.py`
- **Purpose**: Provides unified interface for multiple data providers
- **Pattern**: Factory pattern for provider selection
- **Providers**: SSI, VietStock, CafeF

```python
# Provider-specific implementations
├── ssi_scraper.py      # SSI Securities data provider
├── vietstock_scraper.py # VietStock data provider  
└── cafef_scraper.py    # CafeF data provider
```

#### HTTP Services
- **Location**: `server/stockpal/core/http_service.py`
- **Components**:
  - `HttpJsonService`: JSON API interactions
  - `HttpXlsxService`: Excel file downloads
- **Features**: Session management, error handling, logging

#### Cache Management
- **Strategy**: File-based caching with timestamp validation
- **Location**: Local file system under `server/db/cache/`
- **TTL**: Configurable per data type (default: 24 hours)

### 2. Business Logic Layer

#### Technical Indicators
- **Location**: `server/stockpal/indicator/`
- **Base Class**: `BaseIndicator` (Abstract)
- **Pattern**: Template method pattern

```python
# Indicator hierarchy
BaseIndicator (Abstract)
├── RelativeStrengthIndex (RSI)
├── MACD
├── MovingAverage (SMA, EMA)
├── BollingerBands
├── AverageDirectionalIndex (ADX)
├── StochasticOscillator
└── CommodityChannelIndex (CCI)
```

#### Analysis Services
- **Location**: `server/stockpal/analysis/`
- **Main Component**: `TrendPredictor`
- **Responsibilities**:
  - Market condition detection
  - Adaptive indicator weighting
  - Signal synthesis
  - Price target calculation

#### Signal Synthesis
- **Algorithm**: Weighted combination of multiple indicators
- **Adaptive Weights**: Dynamic adjustment based on market conditions
- **Output**: Entry/exit signals with confidence levels

### 3. Presentation Layer

#### Web Interface
- **Location**: `web_test/`
- **Technology**: HTML5, TailwindCSS, Vanilla JavaScript
- **Features**: 
  - Real-time chart visualization
  - Interactive indicator controls
  - Responsive design

#### REST API
- **Framework**: Flask (planned)
- **Endpoints**: Stock data, analysis results, indicator calculations
- **Format**: JSON responses with standardized error handling

## Data Flow Architecture

### 1. Data Ingestion Flow

```
External API → HTTP Service → Data Scraper → Cache → Business Logic
```

1. **Request Initiation**: User requests analysis for a symbol
2. **Cache Check**: System checks for cached data
3. **API Call**: If cache miss, fetch from external provider
4. **Data Validation**: Validate and normalize incoming data
5. **Cache Storage**: Store validated data with timestamp
6. **Processing**: Pass data to analysis components

### 2. Analysis Flow

```
Price Data → Indicators → Signal Synthesis → Recommendations
```

1. **Data Preparation**: Sort and validate price data
2. **Indicator Calculation**: Calculate all required indicators
3. **Market Condition Detection**: Determine trending vs ranging market
4. **Weight Adjustment**: Adapt indicator weights based on market condition
5. **Signal Generation**: Synthesize signals from weighted indicators
6. **Risk Management**: Apply position sizing and stop-loss calculations

### 3. Logging Flow

```
Component → Logger → File Handler → Log Files (per symbol/indicator)
```

- **Centralized Logging**: `StockPalLogger` configuration
- **Structured Logs**: Separate files per indicator and symbol
- **Log Levels**: DEBUG, INFO, WARNING, ERROR
- **Rotation**: 10MB files with 5 backup copies

## Design Patterns

### 1. Factory Pattern
- **Usage**: DataScraper for provider selection
- **Benefit**: Easy addition of new data providers
- **Implementation**: Provider string maps to concrete scraper class

### 2. Template Method Pattern
- **Usage**: BaseIndicator for common indicator operations
- **Benefit**: Consistent interface across all indicators
- **Implementation**: Abstract methods for calculation, signals, trends

### 3. Strategy Pattern
- **Usage**: Adaptive weighting in TrendPredictor
- **Benefit**: Different strategies for different market conditions
- **Implementation**: Weight adjustment algorithms based on market state

### 4. Observer Pattern (Planned)
- **Usage**: Real-time data updates
- **Benefit**: Automatic UI updates when data changes
- **Implementation**: Event-driven architecture for live data

## Database Design

### File-Based Storage Structure

```
server/db/
├── cache/                  # Cached API responses
│   ├── ssi/               # SSI provider cache
│   ├── vietstock/         # VietStock provider cache
│   └── cafef/             # CafeF provider cache
├── logs/                  # Application logs
│   ├── {SYMBOL}/          # Symbol-specific logs
│   │   ├── rsi.log       # RSI indicator logs
│   │   ├── macd.log      # MACD indicator logs
│   │   └── analysis.log  # Analysis logs
│   └── stockpal.log      # General application log
└── exports/              # Exported data files
    ├── analysis/         # Analysis results
    └── reports/          # Generated reports
```

### Data Models

#### Core Data Structures
- **PriceData**: Base price information
- **DailyPrice/MinutePrice**: Time-specific price data
- **Stock**: Symbol metadata
- **EventData**: Corporate events
- **AnalysisResult**: Analysis output

## Performance Considerations

### 1. Caching Strategy
- **Level 1**: In-memory caching for active analysis
- **Level 2**: File-based caching for API responses
- **TTL**: 24 hours for daily data, 5 minutes for real-time data

### 2. Calculation Optimization
- **Vectorization**: Use NumPy for bulk calculations
- **Lazy Loading**: Calculate indicators only when needed
- **Parallel Processing**: Multi-threading for independent calculations

### 3. Memory Management
- **Data Streaming**: Process large datasets in chunks
- **Garbage Collection**: Explicit cleanup of large objects
- **Resource Pooling**: Reuse HTTP connections

## Security Architecture

### 1. Data Validation
- **Input Sanitization**: Validate all external data
- **Type Checking**: Strict type validation for calculations
- **Range Validation**: Ensure data values are within expected ranges

### 2. Error Handling
- **Graceful Degradation**: Continue operation with partial data
- **Error Logging**: Comprehensive error tracking
- **Fallback Mechanisms**: Alternative data sources on failure

### 3. Rate Limiting
- **Provider Limits**: Respect external API rate limits
- **Backoff Strategy**: Exponential backoff on failures
- **Circuit Breaker**: Temporary disable failing providers

## Scalability Design

### 1. Horizontal Scaling
- **Stateless Design**: No server-side session state
- **Load Balancing**: Distribute requests across instances
- **Database Sharding**: Partition data by symbol or date

### 2. Vertical Scaling
- **Resource Optimization**: Efficient memory and CPU usage
- **Caching Layers**: Reduce computational overhead
- **Algorithm Optimization**: Optimized indicator calculations

## Monitoring and Observability

### 1. Logging Strategy
- **Structured Logging**: JSON format for machine parsing
- **Log Aggregation**: Centralized log collection
- **Performance Metrics**: Calculation timing and accuracy

### 2. Health Checks
- **Data Provider Health**: Monitor API availability
- **Calculation Accuracy**: Validate indicator outputs
- **System Performance**: Track response times and resource usage

## Deployment Architecture

### 1. Development Environment
- **Local Development**: Python virtual environment
- **Testing**: Unit and integration test suites
- **Debugging**: Comprehensive logging and error tracking

### 2. Production Considerations (Future)
- **Containerization**: Docker for consistent deployment
- **Orchestration**: Kubernetes for scaling
- **CI/CD Pipeline**: Automated testing and deployment

## Integration Points

### 1. External APIs
- **SSI Securities**: Primary data provider
- **VietStock**: Alternative data source
- **CafeF**: Backup data provider

### 2. Future Integrations
- **Real-time Data**: WebSocket connections
- **Machine Learning**: TensorFlow/PyTorch integration
- **Cloud Storage**: AWS S3 or Google Cloud Storage

## Technology Stack

### Core Technologies
- **Language**: Python 3.7+
- **Data Processing**: NumPy, Pandas
- **HTTP Client**: Requests with session management
- **Web Framework**: Flask (planned)
- **Frontend**: HTML5, TailwindCSS, Vanilla JavaScript

### Development Tools
- **Testing**: pytest, unittest
- **Code Quality**: pylint, black, mypy
- **Documentation**: Sphinx (planned)
- **Version Control**: Git

### Dependencies
- **Required**: numpy, pandas, requests, dataclasses
- **Optional**: ta-lib, matplotlib, flask
- **Development**: pytest, pylint, black
