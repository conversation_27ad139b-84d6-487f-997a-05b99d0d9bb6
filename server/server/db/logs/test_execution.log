2025-05-26 00:49:06,638 - __main__ - INFO - ================================================================================
2025-05-26 00:49:06,638 - __main__ - INFO - STOCKPAL CLEAN ARCHITECTURE TEST SUITE
2025-05-26 00:49:06,638 - __main__ - INFO - ================================================================================
2025-05-26 00:49:06,638 - __main__ - INFO - Running unit tests...
2025-05-26 00:49:07,357 - __main__ - INFO - Running integration tests...
2025-05-26 00:49:07,392 - __main__ - ERROR - Failed to import integration tests: No module named 'infrastructure.repositories.stock_repository'
2025-05-26 00:49:07,393 - __main__ - INFO - Running performance tests...
2025-05-26 00:49:07,417 - infrastructure.cache.cache_service - INFO - Cache directories initialized at cache
2025-05-26 00:49:07,417 - refactored_main - INFO - Application dependencies initialized successfully
2025-05-26 00:49:07,417 - refactored_main - INFO - Fetching data for VIC
2025-05-26 00:49:07,417 - application.commands.fetch_data_command - INFO - Executing fetch daily prices command for VIC
2025-05-26 00:49:07,417 - core.services.data_service - INFO - Getting daily prices for VIC, 30 days
2025-05-26 00:49:07,423 - core.services.data_service - INFO - Retrieved 30 daily prices from local storage
2025-05-26 00:49:07,423 - application.commands.fetch_data_command - INFO - Successfully fetched 30 daily prices for VIC
2025-05-26 00:49:07,423 - refactored_main - INFO - Successfully fetched 30 price points for VIC
2025-05-26 00:49:07,423 - refactored_main - INFO - Analyzing stock VIC
2025-05-26 00:49:07,423 - application.commands.analyze_stock_command - INFO - Executing analyze single stock command for VIC
2025-05-26 00:49:07,423 - core.services.analysis_service - INFO - Starting analysis for VIC
2025-05-26 00:49:07,423 - core.services.data_service - INFO - Getting daily prices for VIC, 30 days
2025-05-26 00:49:07,427 - core.services.data_service - INFO - Retrieved 30 daily prices from local storage
2025-05-26 00:49:07,431 - core.services.signal_synthesizer - INFO - Synthesizing signals for VIC
2025-05-26 00:49:07,431 - core.services.signal_synthesizer - ERROR - StockPal synthesizer failed: PriceData.__init__() got an unexpected keyword argument 'high_price'
2025-05-26 00:49:07,431 - core.services.signal_synthesizer - ERROR - Failed to synthesize signals for VIC: type object 'MarketCondition' has no attribute 'NEUTRAL'
2025-05-26 00:49:07,431 - core.services.analysis_service - ERROR - Failed to analyze stock VIC: Signal synthesis failed: type object 'MarketCondition' has no attribute 'NEUTRAL'
Traceback (most recent call last):
  File "/home/<USER>/Workspaces/stockpal/server/core/services/signal_synthesizer.py", line 104, in _use_stockpal_synthesizer
    stockpal_price = PriceData(
                     ^^^^^^^^^^
TypeError: PriceData.__init__() got an unexpected keyword argument 'high_price'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Workspaces/stockpal/server/core/services/signal_synthesizer.py", line 77, in synthesize_analysis
    result = self._use_stockpal_synthesizer(symbol, prices, include_zones, include_risk_analysis)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Workspaces/stockpal/server/core/services/signal_synthesizer.py", line 142, in _use_stockpal_synthesizer
    return self._basic_synthesis(symbol, prices, [], None, include_zones, include_risk_analysis)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Workspaces/stockpal/server/core/services/signal_synthesizer.py", line 227, in _basic_synthesis
    "market_condition": MarketCondition.NEUTRAL,
                        ^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: type object 'MarketCondition' has no attribute 'NEUTRAL'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Workspaces/stockpal/server/core/services/analysis_service.py", line 96, in analyze_stock
    synthesis_result = self._signal_synthesizer.synthesize_analysis(
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Workspaces/stockpal/server/core/services/signal_synthesizer.py", line 90, in synthesize_analysis
    raise AnalysisException(f"Signal synthesis failed: {str(e)}")
shared.exceptions.stock_exceptions.AnalysisException: Signal synthesis failed: type object 'MarketCondition' has no attribute 'NEUTRAL'
2025-05-26 00:49:07,433 - refactored_main - ERROR - Analysis failed: Failed to analyze stock VIC: Signal synthesis failed: type object 'MarketCondition' has no attribute 'NEUTRAL'
2025-05-26 00:49:07,433 - refactored_main - INFO - Fetching data for VHM
2025-05-26 00:49:07,433 - application.commands.fetch_data_command - INFO - Executing fetch daily prices command for VHM
2025-05-26 00:49:07,433 - core.services.data_service - INFO - Getting daily prices for VHM, 30 days
2025-05-26 00:49:07,441 - core.services.data_service - INFO - Retrieved 30 daily prices from local storage
2025-05-26 00:49:07,442 - application.commands.fetch_data_command - INFO - Successfully fetched 30 daily prices for VHM
2025-05-26 00:49:07,442 - refactored_main - INFO - Successfully fetched 30 price points for VHM
2025-05-26 00:49:07,442 - refactored_main - INFO - Analyzing stock VHM
2025-05-26 00:49:07,442 - application.commands.analyze_stock_command - INFO - Executing analyze single stock command for VHM
2025-05-26 00:49:07,443 - core.services.analysis_service - INFO - Starting analysis for VHM
2025-05-26 00:49:07,443 - core.services.data_service - INFO - Getting daily prices for VHM, 30 days
2025-05-26 00:49:07,447 - core.services.data_service - INFO - Retrieved 30 daily prices from local storage
2025-05-26 00:49:07,452 - core.services.signal_synthesizer - INFO - Synthesizing signals for VHM
2025-05-26 00:49:07,452 - core.services.signal_synthesizer - ERROR - StockPal synthesizer failed: PriceData.__init__() got an unexpected keyword argument 'high_price'
2025-05-26 00:49:07,452 - core.services.signal_synthesizer - ERROR - Failed to synthesize signals for VHM: type object 'MarketCondition' has no attribute 'NEUTRAL'
2025-05-26 00:49:07,452 - core.services.analysis_service - ERROR - Failed to analyze stock VHM: Signal synthesis failed: type object 'MarketCondition' has no attribute 'NEUTRAL'
Traceback (most recent call last):
  File "/home/<USER>/Workspaces/stockpal/server/core/services/signal_synthesizer.py", line 104, in _use_stockpal_synthesizer
    stockpal_price = PriceData(
                     ^^^^^^^^^^
TypeError: PriceData.__init__() got an unexpected keyword argument 'high_price'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Workspaces/stockpal/server/core/services/signal_synthesizer.py", line 77, in synthesize_analysis
    result = self._use_stockpal_synthesizer(symbol, prices, include_zones, include_risk_analysis)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Workspaces/stockpal/server/core/services/signal_synthesizer.py", line 142, in _use_stockpal_synthesizer
    return self._basic_synthesis(symbol, prices, [], None, include_zones, include_risk_analysis)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Workspaces/stockpal/server/core/services/signal_synthesizer.py", line 227, in _basic_synthesis
    "market_condition": MarketCondition.NEUTRAL,
                        ^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: type object 'MarketCondition' has no attribute 'NEUTRAL'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Workspaces/stockpal/server/core/services/analysis_service.py", line 96, in analyze_stock
    synthesis_result = self._signal_synthesizer.synthesize_analysis(
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Workspaces/stockpal/server/core/services/signal_synthesizer.py", line 90, in synthesize_analysis
    raise AnalysisException(f"Signal synthesis failed: {str(e)}")
shared.exceptions.stock_exceptions.AnalysisException: Signal synthesis failed: type object 'MarketCondition' has no attribute 'NEUTRAL'
2025-05-26 00:49:07,455 - refactored_main - ERROR - Analysis failed: Failed to analyze stock VHM: Signal synthesis failed: type object 'MarketCondition' has no attribute 'NEUTRAL'
2025-05-26 00:49:07,455 - refactored_main - INFO - Fetching data for HPG
2025-05-26 00:49:07,455 - application.commands.fetch_data_command - INFO - Executing fetch daily prices command for HPG
2025-05-26 00:49:07,455 - core.services.data_service - INFO - Getting daily prices for HPG, 30 days
2025-05-26 00:49:07,461 - core.services.data_service - INFO - Retrieved 30 daily prices from local storage
2025-05-26 00:49:07,461 - application.commands.fetch_data_command - INFO - Successfully fetched 30 daily prices for HPG
2025-05-26 00:49:07,461 - refactored_main - INFO - Successfully fetched 30 price points for HPG
2025-05-26 00:49:07,462 - refactored_main - INFO - Analyzing stock HPG
2025-05-26 00:49:07,462 - application.commands.analyze_stock_command - INFO - Executing analyze single stock command for HPG
2025-05-26 00:49:07,462 - core.services.analysis_service - INFO - Starting analysis for HPG
2025-05-26 00:49:07,462 - core.services.data_service - INFO - Getting daily prices for HPG, 30 days
2025-05-26 00:49:07,464 - core.services.data_service - INFO - Retrieved 30 daily prices from local storage
2025-05-26 00:49:07,469 - core.services.signal_synthesizer - INFO - Synthesizing signals for HPG
2025-05-26 00:49:07,469 - core.services.signal_synthesizer - ERROR - StockPal synthesizer failed: PriceData.__init__() got an unexpected keyword argument 'high_price'
2025-05-26 00:49:07,469 - core.services.signal_synthesizer - ERROR - Failed to synthesize signals for HPG: type object 'MarketCondition' has no attribute 'NEUTRAL'
2025-05-26 00:49:07,470 - core.services.analysis_service - ERROR - Failed to analyze stock HPG: Signal synthesis failed: type object 'MarketCondition' has no attribute 'NEUTRAL'
Traceback (most recent call last):
  File "/home/<USER>/Workspaces/stockpal/server/core/services/signal_synthesizer.py", line 104, in _use_stockpal_synthesizer
    stockpal_price = PriceData(
                     ^^^^^^^^^^
TypeError: PriceData.__init__() got an unexpected keyword argument 'high_price'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Workspaces/stockpal/server/core/services/signal_synthesizer.py", line 77, in synthesize_analysis
    result = self._use_stockpal_synthesizer(symbol, prices, include_zones, include_risk_analysis)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Workspaces/stockpal/server/core/services/signal_synthesizer.py", line 142, in _use_stockpal_synthesizer
    return self._basic_synthesis(symbol, prices, [], None, include_zones, include_risk_analysis)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Workspaces/stockpal/server/core/services/signal_synthesizer.py", line 227, in _basic_synthesis
    "market_condition": MarketCondition.NEUTRAL,
                        ^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: type object 'MarketCondition' has no attribute 'NEUTRAL'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Workspaces/stockpal/server/core/services/analysis_service.py", line 96, in analyze_stock
    synthesis_result = self._signal_synthesizer.synthesize_analysis(
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Workspaces/stockpal/server/core/services/signal_synthesizer.py", line 90, in synthesize_analysis
    raise AnalysisException(f"Signal synthesis failed: {str(e)}")
shared.exceptions.stock_exceptions.AnalysisException: Signal synthesis failed: type object 'MarketCondition' has no attribute 'NEUTRAL'
2025-05-26 00:49:07,472 - refactored_main - ERROR - Analysis failed: Failed to analyze stock HPG: Signal synthesis failed: type object 'MarketCondition' has no attribute 'NEUTRAL'
2025-05-26 00:49:07,473 - __main__ - INFO - Running architecture validation...
2025-05-26 00:49:07,475 - infrastructure.cache.cache_service - INFO - Cache directories initialized at cache
2025-05-26 00:49:07,475 - refactored_main - INFO - Application dependencies initialized successfully
2025-05-26 00:49:07,475 - __main__ - INFO - ================================================================================
2025-05-26 00:49:07,475 - __main__ - INFO - TEST EXECUTION SUMMARY
2025-05-26 00:49:07,475 - __main__ - INFO - ================================================================================
2025-05-26 00:49:07,475 - __main__ - INFO - Overall Status: FAILED
2025-05-26 00:49:07,476 - __main__ - INFO - Total Tests: 17
2025-05-26 00:49:07,476 - __main__ - INFO - Failures: 5
2025-05-26 00:49:07,476 - __main__ - INFO - Errors: 3
2025-05-26 00:49:07,476 - __main__ - INFO - Execution Time: 0.84 seconds
2025-05-26 00:49:07,476 - __main__ - INFO - 
Test Suite Results:
2025-05-26 00:49:07,476 - __main__ - INFO -   unit_tests: FAILED (10 tests)
2025-05-26 00:49:07,476 - __main__ - INFO -   integration_tests: FAILED (0 tests)
2025-05-26 00:49:07,476 - __main__ - INFO -   performance_tests: PASSED (3 tests)
2025-05-26 00:49:07,476 - __main__ - INFO -   architecture_validation: FAILED (4 tests)
2025-05-26 00:49:07,476 - __main__ - INFO - 
Failure Details:
2025-05-26 00:49:07,477 - __main__ - INFO -   unit_tests:
2025-05-26 00:49:07,477 - __main__ - INFO -     ERROR: (<tests.test_stockpal_indicators.TestStockPalIndicators testMethod=test_bollinger_bands_calculation>, 'Traceback (most recent call last):\n  File "/home/<USER>/Workspaces/stockpal/server/tests/test_stockpal_indicators.py", line 80, in test_bollinger_bands_calculation\n    bb = BollingerBands("TEST", self.prices, period=20, std_dev=2)\n         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nTypeError: BollingerBands.__init__() got an unexpected keyword argument \'std_dev\'\n')
2025-05-26 00:49:07,477 - __main__ - INFO -     ERROR: (<tests.test_stockpal_indicators.TestStockPalIndicators testMethod=test_performance_with_large_dataset>, 'Traceback (most recent call last):\n  File "/home/<USER>/Workspaces/stockpal/server/tests/test_stockpal_indicators.py", line 265, in test_performance_with_large_dataset\n    self.assertEqual(len(bb_result["upper"]), 730)\n                         ~~~~~~~~~^^^^^^^^^\nKeyError: \'upper\'\n')
2025-05-26 00:49:07,477 - __main__ - INFO -     FAILURE: (<tests.test_stockpal_indicators.TestStockPalIndicators testMethod=test_indicator_edge_cases>, 'Traceback (most recent call last):\n  File "/home/<USER>/Workspaces/stockpal/server/tests/test_stockpal_indicators.py", line 223, in test_indicator_edge_cases\n    self.assertTrue(all(45 <= v <= 55 for v in valid_rsi))\nAssertionError: False is not true\n')
2025-05-26 00:49:07,477 - __main__ - INFO -     FAILURE: (<tests.test_stockpal_indicators.TestStockPalIndicators testMethod=test_insufficient_data_handling>, 'Traceback (most recent call last):\n  File "/home/<USER>/Workspaces/stockpal/server/tests/test_stockpal_indicators.py", line 193, in test_insufficient_data_handling\n    self.assertEqual(len(rs_values), 0)  # Should return empty list\n    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nAssertionError: 10 != 0\n')
2025-05-26 00:49:07,477 - __main__ - INFO -     FAILURE: (<tests.test_stockpal_indicators.TestStockPalIndicators testMethod=test_rs_52_week_calculation>, 'Traceback (most recent call last):\n  File "/home/<USER>/Workspaces/stockpal/server/tests/test_stockpal_indicators.py", line 145, in test_rs_52_week_calculation\n    self.assertGreater(len(valid_values), 50)  # Should have some valid values\n    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nAssertionError: 35 not greater than 50\n')
2025-05-26 00:49:07,477 - __main__ - INFO -     FAILURE: (<tests.test_stockpal_indicators.TestStockPalIndicators testMethod=test_rs_recommendation>, 'Traceback (most recent call last):\n  File "/home/<USER>/Workspaces/stockpal/server/tests/test_stockpal_indicators.py", line 175, in test_rs_recommendation\n    self.assertIn(recommendation, ["Mua mạnh", "Mua", "Giữ", "Bán", "Bán mạnh"])\nAssertionError: \'Buy (Outperforming Benchmark)\' not found in [\'Mua mạnh\', \'Mua\', \'Giữ\', \'Bán\', \'Bán mạnh\']\n')
2025-05-26 00:49:07,477 - __main__ - INFO -   integration_tests:
2025-05-26 00:49:07,477 - __main__ - INFO -     ERROR: Import error: No module named 'infrastructure.repositories.stock_repository'
2025-05-26 00:49:07,477 - __main__ - INFO -   architecture_validation:
2025-05-26 00:49:07,477 - __main__ - INFO -     ERROR: Required component missing: server/stockpal/core/stock.py
2025-05-26 00:49:07,477 - __main__ - INFO -     ERROR: Required component missing: server/stockpal/indicator/rs.py
2025-05-26 00:49:07,477 - __main__ - INFO -     ERROR: Required component missing: server/core/services/signal_synthesizer.py
2025-05-26 00:49:07,477 - __main__ - INFO -     ERROR: Required component missing: server/core/services/performance_monitor.py
2025-05-26 00:49:07,478 - __main__ - INFO -     ERROR: Required component missing: server/infrastructure/repositories/stock_repository.py
2025-05-26 00:49:07,478 - __main__ - INFO - ================================================================================
