#!/usr/bin/env python3
"""
Test script to verify the migration from legacy codebase to clean architecture.

This script tests key functionality to ensure the migration was successful
and all components are working correctly.
"""

import sys
import os
import logging
from datetime import datetime

# Add the server directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from refactored_main import StockPalApplication
from shared.models.stock_models import AnalysisRequest


def setup_logging():
    """Setup logging for the test script."""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )


def test_basic_functionality():
    """Test basic application functionality."""
    print("=" * 60)
    print("TESTING BASIC FUNCTIONALITY")
    print("=" * 60)
    
    try:
        # Initialize application
        print("1. Initializing StockPal application...")
        app = StockPalApplication()
        print("   ✅ Application initialized successfully")
        
        # Test data fetching
        print("2. Testing data fetching...")
        data_result = app.fetch_data("VIC", days_back=30)
        if data_result and data_result.get("success"):
            print(f"   ✅ Data fetched: {data_result['records_fetched']} records")
        else:
            print("   ⚠️  Data fetching failed or returned no data")
        
        # Test stock analysis
        print("3. Testing stock analysis...")
        analysis = app.analyze_stock("VIC", days_back=60, include_zones=True)
        if analysis:
            print(f"   ✅ Analysis completed for VIC")
            print(f"   📊 Current price: {analysis.current_price:.2f}")
            print(f"   📈 Trend: {analysis.trend_analysis.direction.value}")
            print(f"   💡 Recommendation: {analysis.recommendation.value}")
            print(f"   🎯 Buy zones: {len(analysis.buy_zones)}")
            print(f"   🛑 Stop loss zones: {len(analysis.stop_loss_zones)}")
        else:
            print("   ❌ Stock analysis failed")
            return False
        
        return True
        
    except Exception as e:
        print(f"   ❌ Basic functionality test failed: {str(e)}")
        return False


def test_advanced_features():
    """Test advanced features like ML predictions and backtesting."""
    print("\n" + "=" * 60)
    print("TESTING ADVANCED FEATURES")
    print("=" * 60)
    
    try:
        app = StockPalApplication()
        
        # Test ML prediction
        print("1. Testing ML prediction...")
        try:
            prediction = app.get_ml_prediction("VIC", horizon_days=5)
            if prediction:
                print(f"   ✅ ML prediction completed")
                print(f"   📊 Current: {prediction['current_price']:.2f}")
                print(f"   🔮 Predicted: {prediction['predicted_price']:.2f}")
                print(f"   📈 Trend: {prediction['trend_direction']}")
                print(f"   🎯 Confidence: {prediction['confidence']:.1%}")
            else:
                print("   ⚠️  ML prediction returned no data")
        except Exception as e:
            print(f"   ⚠️  ML prediction failed: {str(e)}")
        
        # Test backtesting
        print("2. Testing backtesting...")
        try:
            strategy_params = {"stop_loss": 0.05, "take_profit": 0.12}
            backtest = app.run_backtest("VIC", strategy_params)
            if backtest:
                print(f"   ✅ Backtesting completed")
                print(f"   💰 Total return: {backtest['total_return_percent']:.2f}%")
                print(f"   🎯 Win rate: {backtest['win_rate']:.1f}%")
                print(f"   📊 Sharpe ratio: {backtest['sharpe_ratio']:.2f}")
                print(f"   📉 Max drawdown: {backtest['max_drawdown_percent']:.2f}%")
            else:
                print("   ⚠️  Backtesting returned no data")
        except Exception as e:
            print(f"   ⚠️  Backtesting failed: {str(e)}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Advanced features test failed: {str(e)}")
        return False


def test_batch_analysis():
    """Test batch analysis functionality."""
    print("\n" + "=" * 60)
    print("TESTING BATCH ANALYSIS")
    print("=" * 60)
    
    try:
        app = StockPalApplication()
        
        # Test batch analysis with specific symbols
        print("1. Testing batch analysis with specific symbols...")
        test_symbols = ["VIC", "HPG", "TCB"]
        results = app.run_batch_analysis(symbols=test_symbols, export_results=False)
        
        if results:
            print(f"   ✅ Batch analysis completed")
            print(f"   📊 Total symbols: {results['total_symbols']}")
            print(f"   ✅ Successful: {results['successful_analyses']}")
            print(f"   📈 Success rate: {results['statistics']['success_rate']:.1f}%")
            
            # Show recommendations breakdown
            recommendations = results['statistics']['recommendations']
            print(f"   💡 Recommendations:")
            for rec, count in recommendations.items():
                print(f"      {rec}: {count}")
        else:
            print("   ❌ Batch analysis failed")
            return False
        
        # Test batch analysis in test mode
        print("2. Testing batch analysis in test mode...")
        try:
            test_results = app.run_batch_analysis(test_mode=True, export_results=False)
            if test_results:
                print(f"   ✅ Test mode batch analysis completed")
                print(f"   📊 Analyzed: {test_results['successful_analyses']} symbols")
            else:
                print("   ⚠️  Test mode batch analysis returned no data")
        except Exception as e:
            print(f"   ⚠️  Test mode batch analysis failed: {str(e)}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Batch analysis test failed: {str(e)}")
        return False


def test_cache_functionality():
    """Test cache functionality."""
    print("\n" + "=" * 60)
    print("TESTING CACHE FUNCTIONALITY")
    print("=" * 60)
    
    try:
        app = StockPalApplication()
        
        # Test cache statistics
        print("1. Testing cache statistics...")
        stats = app.get_cache_stats()
        if stats:
            print(f"   ✅ Cache statistics retrieved")
            print(f"   📊 Hit rate: {stats['hit_rate_percent']:.1f}%")
            print(f"   💾 Total size: {stats['total_size_mb']:.1f} MB")
            print(f"   📁 Total entries: {stats['total_entries']}")
        else:
            print("   ⚠️  Cache statistics not available")
        
        # Test cache cleanup
        print("2. Testing cache cleanup...")
        cleanup = app.cleanup_cache()
        if cleanup:
            print(f"   ✅ Cache cleanup completed")
            print(f"   🗑️  Cleaned entries: {cleanup['cleaned_entries']}")
        else:
            print("   ⚠️  Cache cleanup failed")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Cache functionality test failed: {str(e)}")
        return False


def test_export_functionality():
    """Test export functionality."""
    print("\n" + "=" * 60)
    print("TESTING EXPORT FUNCTIONALITY")
    print("=" * 60)
    
    try:
        app = StockPalApplication()
        
        # Test analysis with export
        print("1. Testing analysis with export...")
        analysis = app.analyze_stock("VIC", days_back=30, include_zones=True)
        
        if analysis:
            # Test export service
            export_success = app.export_service.export_analysis_to_web(analysis, "VIC")
            if export_success:
                print("   ✅ Analysis export successful")
            else:
                print("   ⚠️  Analysis export failed")
            
            # Test price history export
            data_result = app.fetch_data("VIC", days_back=30)
            if data_result and data_result.get("success"):
                prices = data_result.get("prices", [])
                if prices:
                    price_export = app.export_service.export_price_history("VIC", prices)
                    if price_export:
                        print("   ✅ Price history export successful")
                    else:
                        print("   ⚠️  Price history export failed")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Export functionality test failed: {str(e)}")
        return False


def main():
    """Main test function."""
    print("🚀 STOCKPAL MIGRATION TEST")
    print("Testing migration from legacy codebase to clean architecture")
    print(f"Test started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    setup_logging()
    
    # Run all tests
    tests = [
        ("Basic Functionality", test_basic_functionality),
        ("Advanced Features", test_advanced_features),
        ("Batch Analysis", test_batch_analysis),
        ("Cache Functionality", test_cache_functionality),
        ("Export Functionality", test_export_functionality)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"\n❌ {test_name} test crashed: {str(e)}")
            results.append((test_name, False))
    
    # Print summary
    print("\n" + "=" * 60)
    print("TEST SUMMARY")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{total} tests passed ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("🎉 ALL TESTS PASSED! Migration is successful.")
        return 0
    else:
        print("⚠️  Some tests failed. Please check the issues above.")
        return 1


if __name__ == "__main__":
    sys.exit(main())
