"""
Integration tests for data providers in StockPal clean architecture.

Tests all data providers (SSI, VietStock, CafeF) with validation through
all architecture layers and performance monitoring.
"""

import unittest
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime, timedelta
from typing import List, Dict, Any

from core.services.performance_monitor import performance_monitor
from infrastructure.repositories.stock_repository import StockRepository
from infrastructure.cache.local_cache import LocalCache
from infrastructure.external.ssi_fetcher import SSIDataFetcher
from infrastructure.external.vietstock_fetcher import VietStockDataFetcher
from infrastructure.external.cafef_fetcher import CafeFDataFetcher
from shared.models.stock_models import PricePoint, StockInfo
from shared.exceptions.stock_exceptions import DataFetchException


class TestDataProviderIntegration(unittest.TestCase):
    """Integration tests for data provider architecture."""

    def setUp(self):
        """Set up test environment."""
        self.cache = LocalCache()
        self.stock_repo = StockRepository(self.cache)
        
        # Test symbols
        self.test_symbols = ["VIC", "VHM", "HPG", "TCB", "BID"]
        
        # Mock price data
        self.mock_prices = self._create_mock_price_data()

    def _create_mock_price_data(self) -> List[PricePoint]:
        """Create mock price data for testing."""
        prices = []
        base_date = datetime.now() - timedelta(days=30)
        
        for i in range(30):
            date = base_date + timedelta(days=i)
            price = PricePoint(
                timestamp=date,
                open_price=100.0 + i,
                high_price=105.0 + i,
                low_price=95.0 + i,
                close_price=102.0 + i,
                volume=1000000 + (i * 10000),
                change_price=2.0,
                change_percent=2.0
            )
            prices.append(price)
        
        return prices

    @patch('infrastructure.external.ssi_fetcher.SSIDataFetcher.fetch_stock_prices')
    def test_ssi_data_fetcher_integration(self, mock_fetch):
        """Test SSI data fetcher integration with performance monitoring."""
        # Setup mock
        mock_fetch.return_value = self.mock_prices
        
        fetcher = SSIDataFetcher()
        
        # Test data fetching with performance monitoring
        operation_id = performance_monitor.start_operation(
            "data_fetch", "SSI", "VIC"
        )
        
        try:
            prices = fetcher.fetch_stock_prices("VIC", days=30)
            performance_monitor.end_operation(operation_id, success=True)
            
            self.assertIsInstance(prices, list)
            self.assertEqual(len(prices), 30)
            self.assertIsInstance(prices[0], PricePoint)
            
        except Exception as e:
            performance_monitor.end_operation(
                operation_id, success=False, error_message=str(e)
            )
            raise

    @patch('infrastructure.external.vietstock_fetcher.VietStockDataFetcher.fetch_stock_prices')
    def test_vietstock_data_fetcher_integration(self, mock_fetch):
        """Test VietStock data fetcher integration."""
        # Setup mock
        mock_fetch.return_value = self.mock_prices
        
        fetcher = VietStockDataFetcher()
        
        # Test data fetching
        operation_id = performance_monitor.start_operation(
            "data_fetch", "VietStock", "VHM"
        )
        
        try:
            prices = fetcher.fetch_stock_prices("VHM", days=30)
            performance_monitor.end_operation(operation_id, success=True)
            
            self.assertIsInstance(prices, list)
            self.assertEqual(len(prices), 30)
            
        except Exception as e:
            performance_monitor.end_operation(
                operation_id, success=False, error_message=str(e)
            )
            raise

    @patch('infrastructure.external.cafef_fetcher.CafeFDataFetcher.fetch_stock_prices')
    def test_cafef_data_fetcher_integration(self, mock_fetch):
        """Test CafeF data fetcher integration."""
        # Setup mock
        mock_fetch.return_value = self.mock_prices
        
        fetcher = CafeFDataFetcher()
        
        # Test data fetching
        operation_id = performance_monitor.start_operation(
            "data_fetch", "CafeF", "HPG"
        )
        
        try:
            prices = fetcher.fetch_stock_prices("HPG", days=30)
            performance_monitor.end_operation(operation_id, success=True)
            
            self.assertIsInstance(prices, list)
            self.assertEqual(len(prices), 30)
            
        except Exception as e:
            performance_monitor.end_operation(
                operation_id, success=False, error_message=str(e)
            )
            raise

    def test_cache_integration(self):
        """Test cache integration across all layers."""
        # Test cache operations with performance monitoring
        operation_id = performance_monitor.start_operation(
            "cache_operation", "LocalCache", "VIC"
        )
        
        try:
            # Store data in cache
            cache_key = "stock_prices_VIC_30"
            self.cache.set(cache_key, self.mock_prices, ttl=3600)
            
            # Retrieve from cache
            cached_data = self.cache.get(cache_key)
            
            performance_monitor.end_operation(operation_id, success=True)
            
            self.assertIsNotNone(cached_data)
            self.assertEqual(len(cached_data), 30)
            
        except Exception as e:
            performance_monitor.end_operation(
                operation_id, success=False, error_message=str(e)
            )
            raise

    @patch('infrastructure.external.ssi_fetcher.SSIDataFetcher.fetch_stock_prices')
    @patch('infrastructure.external.vietstock_fetcher.VietStockDataFetcher.fetch_stock_prices')
    @patch('infrastructure.external.cafef_fetcher.CafeFDataFetcher.fetch_stock_prices')
    def test_provider_fallback_mechanism(self, mock_cafef, mock_vietstock, mock_ssi):
        """Test provider fallback mechanism."""
        # Setup mocks - SSI fails, VietStock succeeds
        mock_ssi.side_effect = DataFetchException("SSI API error")
        mock_vietstock.return_value = self.mock_prices
        mock_cafef.return_value = self.mock_prices
        
        # Test repository with fallback
        try:
            prices = self.stock_repo.get_stock_prices("VIC", days=30, provider="SSI")
            
            # Should fallback to another provider
            self.assertIsInstance(prices, list)
            self.assertGreater(len(prices), 0)
            
        except DataFetchException:
            # If all providers fail, exception should be raised
            pass

    def test_performance_monitoring_stats(self):
        """Test performance monitoring statistics collection."""
        # Simulate multiple operations
        for i in range(5):
            operation_id = performance_monitor.start_operation(
                "data_fetch", "SSI", f"TEST{i}"
            )
            
            # Simulate some processing time
            import time
            time.sleep(0.01)  # 10ms
            
            performance_monitor.end_operation(operation_id, success=True)
        
        # Get performance stats
        stats = performance_monitor.get_performance_stats(
            operation="data_fetch", provider="SSI", hours=1
        )
        
        self.assertGreater(len(stats), 0)
        
        ssi_stats = next((s for s in stats if s.provider == "SSI"), None)
        self.assertIsNotNone(ssi_stats)
        self.assertEqual(ssi_stats.total_calls, 5)
        self.assertEqual(ssi_stats.success_rate, 100.0)

    def test_error_handling_across_layers(self):
        """Test error handling across all architecture layers."""
        # Test with invalid symbol
        with self.assertRaises(Exception):
            operation_id = performance_monitor.start_operation(
                "data_fetch", "SSI", "INVALID"
            )
            
            try:
                # This should fail
                fetcher = SSIDataFetcher()
                prices = fetcher.fetch_stock_prices("INVALID", days=30)
                performance_monitor.end_operation(operation_id, success=True)
                
            except Exception as e:
                performance_monitor.end_operation(
                    operation_id, success=False, error_message=str(e)
                )
                raise

    def test_data_validation_across_providers(self):
        """Test data validation across all providers."""
        providers = [
            ("SSI", SSIDataFetcher()),
            ("VietStock", VietStockDataFetcher()),
            ("CafeF", CafeFDataFetcher())
        ]
        
        for provider_name, fetcher in providers:
            with patch.object(fetcher, 'fetch_stock_prices', return_value=self.mock_prices):
                prices = fetcher.fetch_stock_prices("VIC", days=30)
                
                # Validate data structure
                self.assertIsInstance(prices, list)
                
                for price in prices:
                    self.assertIsInstance(price, PricePoint)
                    self.assertIsInstance(price.timestamp, datetime)
                    self.assertGreater(price.close_price, 0)
                    self.assertGreater(price.volume, 0)
                    self.assertGreaterEqual(price.high_price, price.close_price)
                    self.assertLessEqual(price.low_price, price.close_price)

    def test_concurrent_data_fetching(self):
        """Test concurrent data fetching from multiple providers."""
        import threading
        import time
        
        results = {}
        errors = {}
        
        def fetch_data(provider_name, symbol):
            try:
                operation_id = performance_monitor.start_operation(
                    "data_fetch", provider_name, symbol
                )
                
                # Simulate data fetching
                time.sleep(0.1)  # 100ms simulation
                results[f"{provider_name}_{symbol}"] = self.mock_prices
                
                performance_monitor.end_operation(operation_id, success=True)
                
            except Exception as e:
                errors[f"{provider_name}_{symbol}"] = str(e)
                performance_monitor.end_operation(
                    operation_id, success=False, error_message=str(e)
                )
        
        # Start multiple threads
        threads = []
        for provider in ["SSI", "VietStock", "CafeF"]:
            for symbol in ["VIC", "VHM"]:
                thread = threading.Thread(
                    target=fetch_data, 
                    args=(provider, symbol)
                )
                threads.append(thread)
                thread.start()
        
        # Wait for all threads to complete
        for thread in threads:
            thread.join()
        
        # Check results
        self.assertEqual(len(results), 6)  # 3 providers × 2 symbols
        self.assertEqual(len(errors), 0)

    def test_memory_usage_optimization(self):
        """Test memory usage optimization in data processing."""
        # Test with large dataset
        large_dataset = []
        for i in range(1000):  # 1000 data points
            price = PricePoint(
                timestamp=datetime.now() - timedelta(days=i),
                open_price=100.0,
                high_price=105.0,
                low_price=95.0,
                close_price=102.0,
                volume=1000000,
                change_price=2.0,
                change_percent=2.0
            )
            large_dataset.append(price)
        
        # Test cache with large dataset
        cache_key = "large_dataset_test"
        
        operation_id = performance_monitor.start_operation(
            "cache_operation", "LocalCache", "LARGE_TEST"
        )
        
        try:
            self.cache.set(cache_key, large_dataset, ttl=60)
            retrieved_data = self.cache.get(cache_key)
            
            performance_monitor.end_operation(operation_id, success=True)
            
            self.assertEqual(len(retrieved_data), 1000)
            
        except Exception as e:
            performance_monitor.end_operation(
                operation_id, success=False, error_message=str(e)
            )
            raise


if __name__ == "__main__":
    unittest.main()
