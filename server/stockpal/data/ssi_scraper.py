import os
import re
from dataclasses import dataclass
from datetime import datetime, timed<PERSON><PERSON>
from io import BytesIO
from typing import override

import pandas as pd

from stockpal.core.constants import Constants
from stockpal.core.http_service import HttpJsonService, HttpXlsxService
from stockpal.core.scraper import Scraper
from stockpal.core.stock import DailyPrice, EventData, MinutePrice, PriceData
from stockpal.core.util import Utils


class SsiScraper(Scraper):

    def __init__(self, symbol: str):
        super().__init__(symbol)
        self.http_json_service = HttpJsonService()
        self.http_xlsx_service = HttpXlsxService()

    def realtime_info(self):
        url = f"https://iboard-query.ssi.com.vn/v2/stock/{self._symbol}"
        response = self.http_json_service.get(
            url=url,
            cache_foldername=self._cache_subfolder,
            cache_filename=f"ssi_{self._symbol}_current_price.json",
        )

        if response and "data" in response:
            return SsiStockQuote.from_dict(response["data"])
        return None

    @override
    def prices(self, timeframe_in_minute: bool = False) -> list[PriceData]:
        return (
            self.__fetch_price_from_chart(timeframe_in_minute)
            if timeframe_in_minute
            else self.__fetch_price_details()
        )

    @override
    def events(self) -> list[EventData]:
        time_ranges = self._get_time_ranges()

        events = []
        for start, end in time_ranges:
            # Prepare the URL with the current time range
            url = f"https://iboard-api.ssi.com.vn/statistics/charts/timescale_marks?symbol={self._symbol}&from={start}&to={end}&resolution=1D&language=vi"

            start_str = datetime.fromtimestamp(start).strftime("%Y_%m_%d")
            end_str = datetime.fromtimestamp(end).strftime("%Y_%m_%d")
            cache_filename = f"{self._symbol}_events_{start_str}_{end_str}-ssi.json"

            response = self.http_json_service.get(
                url=url,
                cache_foldername=self._cache_subfolder,
                cache_filename=cache_filename,
            )

            if not response or "data" not in response or "t" not in response["data"]:
                print(f"No data found for {cache_filename}")
                break

            events.append(
                EventData(
                    id=item.get("id", ""),
                    time=item.get("time", 0),
                    texts=item.get("tooltip", []),
                    shape=item.get("shape"),
                    color=item.get("color"),
                    label=item.get("label"),
                )
                for item in response["data"]
            )

        return events

    @override
    def quote(self):
        url = f"https://iboard-api.ssi.com.vn/statistics/company/stock-price?symbol={self._symbol}&page=1&pageSize=10&fromDate=01%2F07%2F2000&toDate=28%2F04%2F2025"

        cache_filename = f"{self._symbol}_quote_ssi.json"
        response = self.http_json_service.get(
            url=url,
            cache_foldername=self._cache_subfolder,
            cache_filename=cache_filename,
        )

        if not response or "data" not in response:
            print(f"No data found for {cache_filename}")
            return None

        return SsiStockQuote.from_dict(response["data"][0])

    @override
    def orders(self):
        order_date = Utils.get_last_trading_date().strftime("%Y_%m_%d")
        print(f"Order date: {order_date}")

        orders = []

        last_id = None
        has_more_data = True

        while has_more_data:
            # Xây dựng URL và tên file cache
            if last_id:
                url = f"https://iboard-query.ssi.com.vn/le-table?stockSymbol={self._symbol}&pageSize=100&lastId={last_id}"
                cache_filename = (
                    f"{self._symbol}_orders_{order_date}_{last_id}-ssi.json"
                )
            else:
                url = f"https://iboard-query.ssi.com.vn/le-table?stockSymbol={self._symbol}&pageSize=100"
                cache_filename = f"{self._symbol}_orders_{order_date}-ssi.json"

            response = self.http_json_service.get(
                url=url,
                cache_foldername=self._cache_subfolder,
                cache_filename=cache_filename,
            )

            if (
                not response
                or "data" not in response
                or "items" not in response["data"]
                or not response["data"]["items"]
            ):
                print(f"No data found for {cache_filename}")
                has_more_data = False
                continue

            items = response["data"]["items"]

            orders.append(
                SsiOrder(
                    _id=item.get("_id", ""),
                    stock_symbol=item.get("stockSymbol", ""),
                    accumulated_volume=item.get("accumulatedVol", 0),
                    accumulated_value=item.get("accumulatedVal", 0.0),
                    volume=item.get("vol", 0),
                    change_type=item.get("changeType", ""),
                    price=item.get("price", 0.0),
                    price_change=item.get("priceChange", 0.0),
                    price_change_percent=item.get("priceChangePercent", 0.0),
                    reference_price=item.get("ref", 0.0),
                    side=item.get("side", ""),
                    time=item.get("time", ""),
                )
                for item in items
            )

            # Set last_id for the next iteration
            if items:
                last_id = items[-1]["_id"]
            else:
                has_more_data = False

        return orders

    def download_orders_file(self):
        url = f"https://iboard-api.ssi.com.vn/statistics/reports/export-le-table?stockSymbol={self._symbol}&lang=vi"

        run_date = datetime.now().strftime("%Y_%m_%d")
        cache_filename = f"{self._symbol}_le_table_vi_{run_date}-ssi.json"

        response = self.http_xlsx_service.get(
            url=url,
            cache_foldername=self._cache_subfolder,
            cache_filename=cache_filename,
        )

        if not response:
            print(f"No data found for {cache_filename}")
            return None

        df = pd.read_excel(BytesIO(response))

        data = []
        for _, row in df.iterrows():
            # Convert time string to datetime
            time_str: str = str(row["Thời gian"])
            volume = int(row["KL"])
            price = float(str(row["Giá"]).replace(",", ""))
            price_change = (
                float(str(row["+/-"]).replace(",", "")) if row["+/-"] != "" else 0
            )
            price_change_percent = (
                float(str(row["+/-%"]).replace(",", "")) if row["+/-%"] != "" else 0
            )
            action = row["M/B"] if row["M/B"] != "-" else None

            # Create datetime object for today with the given time
            today = datetime.now().date()
            time_obj = datetime.strptime(time_str, "%H:%M:%S").time()
            trade_time = datetime.combine(today, time_obj)

            data.append(
                {
                    "trade_time": time_str,
                    "volume": volume,
                    "price": price,
                    "price_change": price_change,
                    "price_change_percent": price_change_percent,
                    "action": action,
                }
            )

        return data

    def __fetch_price_details(self) -> list[PriceData]:
        time_ranges = self._get_time_ranges()

        prices: list[PriceData] = []
        dup_dates = []

        for start, end in time_ranges:
            start_str = datetime.fromtimestamp(start).strftime("%d/%m/%Y")
            end_str = datetime.fromtimestamp(end).strftime("%d/%m/%Y")

            cache_new_start_str = datetime.fromtimestamp(start).strftime("%Y_%m_%d")
            cache_new_end_str = datetime.fromtimestamp(end).strftime("%Y_%m_%d")

            last_trading_date = Utils.get_last_trading_date()
            is_last_day = (end == int(last_trading_date.timestamp())) or (
                end == int((last_trading_date + timedelta(days=1)).timestamp())
            )

            page = 1
            has_more_data = True

            while has_more_data:
                url = f"https://iboard-api.ssi.com.vn/statistics/company/ssmi/stock-info?symbol={self._symbol}&fromDate={start_str}&toDate={end_str}&page={page}&pageSize=500"

                # Old cache file name format
                cache_filename = f"{self._symbol}_price_details_{start_str}_{end_str}_page_{page}-ssi.json"

                cache_full_path = os.path.join(
                    Utils.get_cache_dir(),
                    self._cache_subfolder if self._cache_subfolder else "",
                    (
                        re.sub(r"[^a-zA-Z0-9._-]", "_", cache_filename)
                        if cache_filename
                        else ""
                    ),
                )

                # If cache file does not exist, use new cache name format
                if is_last_day or not os.path.exists(cache_full_path):
                    cache_filename = f"{self._symbol}_price_details_{cache_new_start_str}_{cache_new_end_str}_page_{page}-ssi.json"
                    if not is_last_day:
                        print(
                            f"\u2139 Old cache file does not exist. Generating new format cache name {cache_filename}"
                        )

                response = self.http_json_service.get(
                    url=url,
                    cache_foldername=self._cache_subfolder,
                    cache_filename=cache_filename,
                    use_cache=not is_last_day,
                    get_cache_result=False
                )

                if not response or "data" not in response or not response["data"]:
                    print(f"No data found for {cache_filename}")
                    has_more_data = False
                    break

                # Check if we have fewer items than the page size, indicating no more data
                if len(response["data"]) < 500:
                    has_more_data = False

                page += 1

                for item in response["data"]:
                    if item["tradingDate"] in dup_dates:
                        continue

                    try:
                        dup_dates.append(item["tradingDate"])

                        # Get "14/04/2025" from "14/04/2025 00:00:00"
                        date_str = item["tradingDate"].split()[0]
                        date_obj = datetime.strptime(date_str, "%d/%m/%Y")
                        timestamp = int(date_obj.timestamp())

                        prices.append(
                            DailyPrice(
                                symbol=self._symbol,
                                timestamp=timestamp,
                                open_price=float(
                                    item.get(
                                        "openPrice" if "openPrice" in item else "open",
                                        0,
                                    )
                                ),
                                close_price=float(
                                    item.get(
                                        (
                                            "closePrice"
                                            if "closePrice" in item
                                            else "close"
                                        ),
                                        0,
                                    )
                                ),
                                highest_price=float(
                                    item.get(
                                        (
                                            "highestPrice"
                                            if "highestPrice" in item
                                            else "high"
                                        ),
                                        0,
                                    )
                                ),
                                lowest_price=float(
                                    item.get(
                                        (
                                            "lowestPrice"
                                            if "lowestPrice" in item
                                            else "low"
                                        ),
                                        0,
                                    )
                                ),
                                change_price=float(
                                    self.__safe_get_number(
                                        item,
                                        (
                                            "priceChange"
                                            if "priceChange" in item
                                            else "priceChanged"
                                        ),
                                    )
                                ),
                                change_price_percent=float(
                                    self.__safe_get_number(item, "perPriceChange")
                                ),
                                average_price=float(
                                    self.__safe_get_number(
                                        item,
                                        (
                                            "averagePrice"
                                            if "averagePrice" in item
                                            else "avgPrice"
                                        ),
                                    )
                                ),
                                close_price_adjusted=float(
                                    self.__safe_get_number(item, "closePriceAdjusted")
                                ),
                                ceiling_price=float(
                                    self.__safe_get_number(item, "ceilingPrice")
                                ),
                                floor_price=float(
                                    self.__safe_get_number(item, "floorPrice")
                                ),
                                reference_price=float(
                                    self.__safe_get_number(item, "refPrice")
                                ),
                                match_volume=float(
                                    self.__safe_get_number(item, "totalMatchVol")
                                ),
                                match_value=float(
                                    self.__safe_get_number(item, "totalMatchVal")
                                ),
                                deal_volume=float(
                                    self.__safe_get_number(item, "totalDealVol")
                                ),
                                deal_value=float(
                                    self.__safe_get_number(item, "totalDealVal")
                                ),
                                foreign_current_room=self.__safe_get_number(
                                    item, "foreignCurrentRoom"
                                ),
                                foreign_buy_volume=float(
                                    self.__safe_get_number(item, "foreignBuyVolTotal")
                                ),
                                foreign_buy_value=float(
                                    self.__safe_get_number(item, "foreignBuyValTotal")
                                ),
                                foreign_sell_volume=float(
                                    self.__safe_get_number(item, "foreignSellVolTotal")
                                ),
                                foreign_sell_value=float(
                                    self.__safe_get_number(item, "foreignSellValTotal")
                                ),
                                foreign_net_volume=float(
                                    self.__safe_get_number(item, "netBuySellVol")
                                ),
                                foreign_net_value=float(
                                    self.__safe_get_number(item, "netBuySellVal")
                                ),
                                foreign_match_buy_volume=float(
                                    self.__safe_get_number(item, "foreignBuyVolMatched")
                                ),
                                foreign_deal_buy_volume=float(
                                    self.__safe_get_number(item, "foreignBuyVolDeal")
                                ),
                                buy_trade_quantity=int(
                                    self.__safe_get_number(item, "totalBuyTrade")
                                ),
                                buy_trade_volume=float(
                                    self.__safe_get_number(item, "totalBuyTradeVol")
                                ),
                                sell_trade_quantity=int(
                                    self.__safe_get_number(item, "totalSellTrade")
                                ),
                                sell_trade_volume=float(
                                    self.__safe_get_number(item, "totalSellTradeVol")
                                ),
                            )
                        )
                    except Exception as e:
                        print(f"Error processing item: {item}")
                        print(f"Error: {e}")

        return prices

    def __safe_get_number(self, item, prop: str):
        return item.get(prop, 0) if item.get(prop, 0) is not None else 0

    def __fetch_price_from_chart(
        self, timeframe_in_minute: bool = False
    ) -> list[PriceData]:
        resolution = "1" if timeframe_in_minute else "1D"
        resolution_str = "minute" if timeframe_in_minute else "day"
        datetime_format = "%Y_%m_%d_%H:%M" if timeframe_in_minute else "%Y_%m_%d"

        time_ranges = self._get_time_ranges()

        prices: list[PriceData] = []
        processed_txns = []

        for start, end in time_ranges:
            url = f"https://iboard-api.ssi.com.vn/statistics/charts/history?resolution={resolution}&symbol={self._symbol}&from={start}&to={end}"

            start_str = datetime.fromtimestamp(start).strftime(datetime_format)
            end_str = datetime.fromtimestamp(end).strftime(datetime_format)
            cache_filename = (
                f"{self._symbol}_{resolution_str}_prices_{start_str}_{end_str}-ssi.json"
            )

            response = self.http_json_service.get(
                url=url,
                cache_foldername=self._cache_subfolder,
                cache_filename=cache_filename,
            )

            # Check if 't' is empty or not present in the response data
            if (
                not response
                or "data" not in response
                or "t" not in response["data"]
                or not response["data"]["t"]
            ):
                print(f"No data found for {cache_filename}")

                if self._stop_scraping(start):
                    break

            history = SsiPrice(
                timestamps=response["data"].get("t", []),
                open_prices=response["data"].get("o", []),
                close_prices=response["data"].get("c", []),
                high_prices=response["data"].get("h", []),
                low_prices=response["data"].get("l", []),
                volumes=response["data"].get("v", []),
            )

            for i in range(len(history.timestamps)):
                if history.timestamps[i] in processed_txns:
                    print(f"Duplicate timestamp {history.timestamps[i]}, skipping...")
                    continue

                processed_txns.append(history.timestamps[i])

                if timeframe_in_minute:
                    prices.append(
                        MinutePrice(
                            symbol=self._symbol,
                            timestamp=history.timestamps[i],
                            open_price=float(history.open_prices[i]),
                            close_price=float(history.close_prices[i]),
                            highest_price=float(history.high_prices[i]),
                            lowest_price=float(history.low_prices[i]),
                            match_volume=float(history.volumes[i]),
                        )
                    )
                else:
                    prices.append(
                        DailyPrice(
                            symbol=self._symbol,
                            timestamp=history.timestamps[i],
                            open_price=float(history.open_prices[i]),
                            close_price=float(history.close_prices[i]),
                            highest_price=float(history.high_prices[i]),
                            lowest_price=float(history.low_prices[i]),
                            match_volume=float(history.volumes[i]),
                        )
                    )

        # Sort by timestamp in descending order
        prices.sort(key=lambda x: x.timestamp, reverse=True)

        for i in range(0, len(prices) - 1, 1):
            prices[i].reference_price = (
                prices[i + 1].close_price if (i + 1) < len(prices) else 0
            )
            prices[i].change_price = round(
                prices[i].close_price - prices[i].reference_price, 2
            )
            # Calculate ceiling and floor prices by adding/subtracting 7% of the reference price
            prices[i].ceiling_price = round(prices[i].reference_price * 1.07, 2)
            prices[i].floor_price = round(prices[i].reference_price / 1.07, 2)

        return prices


@dataclass
class SsiStockQuote:
    """Data Transfer Object for SSI API stock data response."""

    # Stock identification
    symbol: str  # Stock symbol (ss)
    exchange: str  # Exchange code (e)
    stock_number: str  # Stock number (sn)
    company_name_en: str  # Company name in English (ce)
    company_name_vn: str  # Company name in Vietnamese (cv)

    # Price information
    average_price: float  # Average price (ap)
    ceiling_price: float  # Ceiling price (c)
    floor_price: float  # Floor price (f)
    high_price: float  # High price (h)
    low_price: float  # Low price (l)
    open_price: float  # Open price (o)
    match_price: float  # Match price (mp)
    reference_price: float  # Reference price (r)
    last_match_price: float  # Last match price (lmp)
    change_price: float  # Change price (cp)

    # Volume information
    last_volume: int  # Last volume (lv)
    match_volume: int  # Match volume (mv)
    total_shares: int  # Total shares (ls)
    market_cap: float  # Market capitalization (mcp)
    shares_outstanding: int  # Shares outstanding (so)

    # Order book information
    bid1_price: float  # Best bid price (b1)
    bid1_volume: int  # Best bid volume (b1v)
    bid2_price: float  # Second best bid price (b2)
    bid2_volume: int  # Second best bid volume (b2v)
    bid3_price: float  # Third best bid price (b3)
    bid3_volume: int  # Third best bid volume (b3v)

    ask1_price: float  # Best ask price (o1)
    ask1_volume: int  # Best ask volume (o1v)
    ask2_price: float  # Second best ask price (o2)
    ask2_volume: int  # Second best ask volume (o2v)
    ask3_price: float  # Third best ask price (o3)
    ask3_volume: int  # Third best ask volume (o3v)

    # Trading information
    market_status: str  # Market status (os)
    trading_status: str  # Trading status (s)
    total_trading_value: float  # Total trading value (mtv)
    total_trading_shares: int  # Total trading shares (tsh)
    trading_unit: int  # Trading unit (tu)

    # Additional information
    indices: list[str]  # List of indices the stock belongs to (ig)
    last_trading_date: str | None  # Last trading date (ltd)
    last_update_time: int  # Last update time (ltu)

    @classmethod
    def from_dict(cls, data: dict) -> "SsiStockQuote":
        """Create a StockData instance from JSON response."""
        return cls(
            symbol=data.get("ss", ""),
            exchange=data.get("e", ""),
            stock_number=data.get("sn", ""),
            company_name_en=data.get("ce", ""),
            company_name_vn=data.get("cv", ""),
            average_price=float(data.get("ap", 0)),
            ceiling_price=float(data.get("c", 0)),
            floor_price=float(data.get("f", 0)),
            high_price=float(data.get("h", 0)),
            low_price=float(data.get("l", 0)),
            open_price=float(data.get("o", 0)),
            match_price=float(data.get("mp", 0)),
            reference_price=float(data.get("r", 0)),
            last_match_price=float(data.get("lmp", 0)),
            change_price=float(data.get("cp", 0)),
            last_volume=int(data.get("lv", 0)),
            match_volume=int(data.get("mv", 0)),
            total_shares=int(data.get("ls", 0)),
            market_cap=float(data.get("mcp", 0)),
            shares_outstanding=int(data.get("so", 0)),
            bid1_price=float(data.get("b1", 0)),
            bid1_volume=int(data.get("b1v", 0)),
            bid2_price=float(data.get("b2", 0)),
            bid2_volume=int(data.get("b2v", 0)),
            bid3_price=float(data.get("b3", 0)),
            bid3_volume=int(data.get("b3v", 0)),
            ask1_price=float(data.get("o1", 0)),
            ask1_volume=int(data.get("o1v", 0)),
            ask2_price=float(data.get("o2", 0)),
            ask2_volume=int(data.get("o2v", 0)),
            ask3_price=float(data.get("o3", 0)),
            ask3_volume=int(data.get("o3v", 0)),
            market_status=data.get("os", ""),
            trading_status=data.get("s", ""),
            total_trading_value=float(data.get("mtv", 0)),
            total_trading_shares=int(data.get("tsh", 0)),
            trading_unit=int(data.get("tu", 0)),
            indices=data.get("ig", []),
            last_trading_date=data.get("ltd"),
            last_update_time=int(data.get("ltu", 0)),
        )


@dataclass
class SsiPrice:
    timestamps: list[int]  # List of timestamps
    open_prices: list[float]  # List of opening prices
    close_prices: list[float]  # List of closing prices
    high_prices: list[float]  # List of highest prices
    low_prices: list[float]  # List of lowest prices
    volumes: list[int]  # List of trading volumes


@dataclass
class SsiOrder:
    _id: str
    stock_symbol: str
    accumulated_volume: int
    accumulated_value: float
    volume: int
    change_type: str
    price: float
    price_change: float
    price_change_percent: float
    reference_price: float
    side: str
    time: str
