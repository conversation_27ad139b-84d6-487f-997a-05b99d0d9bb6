import time
from typing import Any, Dict, List

from stockpal.core import PriceData
from stockpal.core.logging_config import StockPalLogger

from .base import BaseIndicator

"""
This implementation:

1. Calculates RSI using the standard formula with a default period of 14 days
2. Handles the initial period by returning None values (or 50 if not enough data)
3. Uses the smoothed calculation method for subsequent values
4. Includes a helper method to identify overbought/oversold signals
5. Returns RSI values that can be used for visualization or further analysis

The implementation follows the same pattern as other indicators in your codebase, like the MovingAverage class.

Tín hiệu lực mua/bán của RSI:
- Dải giá trị: 0-100
- Lực mua cao/bán yếu: RSI > 70 (vùng quá mua)
  + Hành động: <PERSON><PERSON> nhắc bán/chốt lời, thận trọng khi mua mới
- Lực bán cao/mua yếu: RSI < 30 (vùng quá bán)
  + Hành động: <PERSON><PERSON> nhắc mua, thận trọng khi bán
- Thị trường cân bằng: RSI = 40-60
  + Hành động: <PERSON>ợ<PERSON> tín hiệu rõ ràng hơn, theo dõi các chỉ báo khác
"""


class RelativeStrengthIndex(BaseIndicator):
    """
    Relative Strength Index (RSI) indicator implementation.

    The RSI is a momentum oscillator that measures the speed and magnitude of price changes.
    It oscillates between 0 and 100 and is typically used to identify overbought and oversold
    conditions in a stock.

    Formula:
        RSI = 100 - (100 / (1 + RS))
        RS = Average Gain / Average Loss

    Interpretation:
        - RSI > 70: Overbought condition (potential sell signal)
        - RSI < 30: Oversold condition (potential buy signal)
        - RSI 40-60: Neutral/balanced market condition

    Attributes:
        symbol (str): Stock symbol
        prices (List[PriceData]): Historical price data
        period (int): Calculation period (default: 14)
        logger: Logger instance for this indicator
    """

    def __init__(self, symbol: str, prices: List[PriceData], period: int = 14):
        """
        Initialize RSI indicator.

        Args:
            symbol (str): Stock symbol
            prices (List[PriceData]): Historical price data
            period (int): RSI calculation period (default: 14)

        Raises:
            ValueError: If period is less than 1 or prices list is empty
        """
        super().__init__(symbol, prices, period=period)

        if period < 1:
            raise ValueError("RSI period must be at least 1")
        if not prices:
            raise ValueError("Price data cannot be empty")

        self.period = period
        self.logger = StockPalLogger.get_indicator_logger("rsi", symbol)

        self.logger.info(f"Initialized RSI indicator for {symbol} with period {period}")
        self.logger.debug(f"Price data points: {len(prices)}")

    def calculate(self) -> List[float]:
        """
        Calculate RSI values for the price data.

        The calculation follows the standard RSI formula:
        1. Calculate price changes between consecutive periods
        2. Separate gains and losses
        3. Calculate initial average gain and loss over the period
        4. Use smoothed averages for subsequent calculations
        5. Apply RSI formula: 100 - (100 / (1 + RS))

        Returns:
            List[float]: List of RSI values corresponding to each price point.
                        Returns None for periods where calculation is not possible.

        Raises:
            ValueError: If insufficient data for calculation
        """
        start_time = time.time()

        self.logger.debug(f"Starting RSI calculation for {self.symbol}")
        StockPalLogger.log_calculation_start(
            self.logger, "RSI", self.symbol,
            {"period": self.period, "data_points": len(self.prices)}
        )

        # Check if we have enough data
        if len(self.prices) <= self.period:
            self.logger.warning(f"Insufficient data for RSI calculation: "
                              f"{len(self.prices)} points, need > {self.period}")
            # Return default neutral values when insufficient data
            result = [50.0] * len(self.prices)
            self.logger.info(f"Returned default RSI values (50.0) for insufficient data")
            return result

        # Sort prices by timestamp to ensure chronological order
        sorted_prices = sorted(self.prices, key=lambda x: x.timestamp)
        close_prices = [price.close_price for price in sorted_prices]

        self.logger.debug(f"Processing {len(close_prices)} price points")

        # Calculate price changes between consecutive periods
        price_changes = [
            close_prices[i] - close_prices[i - 1]
            for i in range(1, len(close_prices))
        ]

        # Initialize RSI values array with None for initial period
        rsi_values = [None] * self.period

        # Calculate initial gains and losses for the first period
        gains = [max(0, change) for change in price_changes[:self.period]]
        losses = [abs(min(0, change)) for change in price_changes[:self.period]]

        # Calculate initial average gain and loss
        avg_gain = sum(gains) / self.period
        avg_loss = sum(losses) / self.period

        self.logger.debug(f"Initial avg_gain: {avg_gain:.4f}, avg_loss: {avg_loss:.4f}")

        # Calculate first RSI value
        if avg_loss == 0:
            # Avoid division by zero - all gains, no losses
            rsi_values.append(100.0)
            self.logger.debug("First RSI: 100.0 (no losses)")
        else:
            rs = avg_gain / avg_loss
            first_rsi = 100 - (100 / (1 + rs))
            rsi_values.append(first_rsi)
            self.logger.debug(f"First RSI: {first_rsi:.2f} (RS: {rs:.4f})")

        # Calculate subsequent RSI values using smoothed averages
        for i in range(self.period, len(price_changes)):
            current_gain = max(0, price_changes[i])
            current_loss = abs(min(0, price_changes[i]))

            # Use smoothed moving average (Wilder's smoothing)
            avg_gain = ((avg_gain * (self.period - 1)) + current_gain) / self.period
            avg_loss = ((avg_loss * (self.period - 1)) + current_loss) / self.period

            if avg_loss == 0:
                # All gains, no losses
                rsi_values.append(100.0)
            else:
                rs = avg_gain / avg_loss
                rsi = 100 - (100 / (1 + rs))
                rsi_values.append(rsi)

        # Round values to 2 decimal places for consistency
        rsi_values = [round(v, 2) if v is not None else None for v in rsi_values]

        # Log calculation completion
        calculation_time = time.time() - start_time
        valid_values = len([v for v in rsi_values if v is not None])

        StockPalLogger.log_calculation_end(
            self.logger, "RSI", self.symbol, valid_values, calculation_time
        )

        self.logger.debug(f"RSI calculation completed: {valid_values} valid values")
        if valid_values > 0:
            latest_rsi = next((v for v in reversed(rsi_values) if v is not None), None)
            self.logger.info(f"Latest RSI value: {latest_rsi}")

        return rsi_values

    def get_signals(self, overbought: float = 70, oversold: float = 30) -> List[Dict[str, Any]]:
        """
        Identify trading signals based on RSI values.
        Args:
            overbought (float): RSI threshold for overbought condition
            oversold (float): RSI threshold for oversold condition
        Returns:
            List[Dict]: List of signal dictionaries
        """
        rsi_values = self.calculate()
        signals = []
        sorted_prices = sorted(self.prices, key=lambda x: x.timestamp)
        for i, rsi in enumerate(rsi_values):
            if rsi is None:
                continue
            if i >= len(sorted_prices):
                break
            signal_type = ""
            buying_power = ""
            action = ""
            if rsi >= overbought:
                signal_type = "overbought"
                buying_power = "Lực mua cao, lực bán yếu"
                action = "Cân nhắc bán/chốt lời, thận trọng khi mua mới"
            elif rsi <= oversold:
                signal_type = "oversold"
                buying_power = "Lực bán cao, lực mua yếu"
                action = "Cân nhắc mua, thận trọng khi bán"
            elif 40 <= rsi <= 60:
                signal_type = "neutral"
                buying_power = "Thị trường cân bằng"
                action = "Đợi tín hiệu rõ ràng hơn"
            elif rsi > 60:
                signal_type = "bullish"
                buying_power = "Lực mua tăng"
                action = "Theo dõi các tín hiệu khác để xác nhận"
            elif rsi < 40:
                signal_type = "bearish"
                buying_power = "Lực bán tăng"
                action = "Theo dõi các tín hiệu khác để xác nhận"
            signals.append({
                "timestamp": sorted_prices[i].timestamp,
                "price": sorted_prices[i].close_price,
                "value": rsi,
                "signal": signal_type,
                "buying_power": buying_power,
                "action": action
            })
        return signals

    def predict_trend(self) -> Dict[str, Any]:
        """
        Predict the trend based on the latest RSI value.
        Returns:
            Dict[str, Any]: Trend direction and confidence
        """
        rsi_values = self.calculate()
        latest_rsi = next((v for v in reversed(rsi_values) if v is not None), 50.0)
        if latest_rsi > 60:
            return {"trend": "uptrend", "confidence": (latest_rsi - 60) / 40}
        elif latest_rsi < 40:
            return {"trend": "downtrend", "confidence": (40 - latest_rsi) / 40}
        else:
            return {"trend": "sideways", "confidence": 1 - abs(latest_rsi - 50) / 10}

    def get_recommendation(self) -> str:
        """
        Generate a recommendation (Buy/Sell/Hold) based on the latest RSI value.
        Returns:
            str: Recommendation string
        """
        rsi_values = self.calculate()
        latest_rsi = next((v for v in reversed(rsi_values) if v is not None), 50.0)
        if latest_rsi >= 70:
            return "Sell (Overbought)"
        elif latest_rsi <= 30:
            return "Buy (Oversold)"
        else:
            return "Hold (Neutral)"
