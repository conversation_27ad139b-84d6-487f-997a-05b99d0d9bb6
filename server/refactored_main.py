"""
Refactored main application using the new clean architecture.

This module demonstrates how to use the new refactored components
to perform data fetching and stock analysis operations.
"""

import logging
import sys
from datetime import datetime
from typing import List, Optional

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

# Import the new architecture components
from shared.models.stock_models import (
    AnalysisRequest, DataFetchRequest, TechnicalIndicator, SignalType,
    ConfidenceLevel, MarketCondition
)
from shared.exceptions.stock_exceptions import (
    StockPalException, DataFetchException, AnalysisException,
    SymbolNotFoundException, InsufficientDataException
)

from infrastructure.repositories.price_repository import SqlitePriceRepository
from infrastructure.repositories.symbol_repository import SqliteSymbolRepository
from infrastructure.external.data_fetcher import ExternalDataFetcher
from infrastructure.cache.cache_service import CacheService
from infrastructure.cache.cache_manager import CacheManager

from core.processors.indicator_processor import IndicatorProcessor
from core.services.data_service import DataService
from core.services.analysis_service import AnalysisService
from core.services.export_service import ExportService
from core.services.signal_synthesizer import SignalSynthesizer
from core.analytics.ml_analytics_service import MLAnalyticsService
from core.analytics.backtesting_service import BacktestingService

from application.commands.fetch_data_command import FetchDataCommand
from application.commands.analyze_stock_command import AnalyzeStockCommand
from application.use_cases.batch_analysis_use_case import BatchAnalysisUseCase


logger = logging.getLogger(__name__)


class StockPalApplication:
    """Main application class using the refactored architecture."""

    def __init__(self):
        """Initialize the application with all dependencies."""
        self._setup_dependencies()

    def _setup_dependencies(self):
        """Setup dependency injection for the application."""
        try:
            # Infrastructure layer
            self.price_repository = SqlitePriceRepository()
            self.symbol_repository = SqliteSymbolRepository()
            self.data_fetcher = ExternalDataFetcher()

            # Cache layer
            self.cache_service = CacheService(cache_dir="cache", default_ttl=3600)
            self.cache_manager = CacheManager(self.cache_service)

            # Core layer
            self.indicator_processor = IndicatorProcessor()
            self.data_service = DataService(
                price_repository=self.price_repository,
                symbol_repository=self.symbol_repository,
                data_fetcher=self.data_fetcher
            )
            # Inject cache manager into data service
            self.data_service._cache_manager = self.cache_manager

            self.analysis_service = AnalysisService(
                data_service=self.data_service,
                indicator_processor=self.indicator_processor
            )

            # Export and synthesis services
            self.export_service = ExportService()
            self.batch_analysis_use_case = BatchAnalysisUseCase(
                analysis_service=self.analysis_service,
                export_service=self.export_service,
                symbol_repository=self.symbol_repository
            )

            # Analytics layer
            self.ml_analytics_service = MLAnalyticsService()
            self.backtesting_service = BacktestingService(
                initial_capital=100000.0,
                commission_rate=0.0015
            )

            # Application layer
            self.fetch_data_command = FetchDataCommand(self.data_service)
            self.analyze_stock_command = AnalyzeStockCommand(self.analysis_service)

            logger.info("Application dependencies initialized successfully")

        except Exception as e:
            logger.error(f"Failed to initialize application dependencies: {str(e)}")
            raise

    def fetch_stock_data(
        self,
        symbol: str,
        days: int = 365,
        force_refresh: bool = False
    ) -> bool:
        """
        Fetch stock data for a symbol.

        Args:
            symbol: Stock symbol
            days: Number of days to fetch
            force_refresh: Whether to force refresh from external source

        Returns:
            True if successful, False otherwise
        """
        try:
            logger.info(f"Fetching data for {symbol}")

            prices = self.fetch_data_command.fetch_daily_prices(
                symbol=symbol,
                days=days,
                force_refresh=force_refresh
            )

            logger.info(f"Successfully fetched {len(prices)} price points for {symbol}")
            return True

        except SymbolNotFoundException as e:
            logger.error(f"Symbol not found: {str(e)}")
            return False
        except DataFetchException as e:
            logger.error(f"Data fetch failed: {str(e)}")
            return False
        except Exception as e:
            logger.error(f"Unexpected error fetching data for {symbol}: {str(e)}")
            return False

    def analyze_stock(
        self,
        symbol: str,
        days_back: int = 365,
        include_zones: bool = True,
        include_risk_analysis: bool = True
    ) -> Optional[dict]:
        """
        Analyze a stock and return results.

        Args:
            symbol: Stock symbol to analyze
            days_back: Number of days of historical data
            include_zones: Whether to include trading zones
            include_risk_analysis: Whether to include risk analysis

        Returns:
            Analysis results as dictionary, or None if failed
        """
        try:
            logger.info(f"Analyzing stock {symbol}")

            analysis = self.analyze_stock_command.analyze_single_stock(
                symbol=symbol,
                days_back=days_back,
                include_zones=include_zones,
                include_risk_analysis=include_risk_analysis
            )

            # Convert to dictionary for easier handling
            result = {
                "symbol": analysis.symbol,
                "current_price": analysis.current_price,
                "analysis_date": analysis.analysis_date.isoformat(),
                "price_change": analysis.price_change,
                "price_change_percent": analysis.price_change_percent,
                "recommendation": analysis.recommendation.value,
                "trend": {
                    "direction": analysis.trend_analysis.direction.value,
                    "strength": analysis.trend_analysis.strength,
                    "confidence": analysis.trend_analysis.confidence.value
                },
                "market_condition": {
                    "condition": analysis.market_condition.condition,
                    "volatility": analysis.market_condition.volatility,
                    "volume_trend": analysis.market_condition.volume_trend
                },
                "confidence_score": analysis.confidence_score,
                "technical_summary": analysis.technical_summary,
                "indicators": [
                    {
                        "name": ind.name,
                        "value": ind.value,
                        "signal": ind.signal.value,
                        "confidence": ind.confidence.value
                    }
                    for ind in analysis.technical_indicators
                ],
                "buy_zones": [
                    {
                        "price": zone.price,
                        "confidence": zone.confidence.value,
                        "reason": zone.reason
                    }
                    for zone in analysis.buy_zones
                ] if include_zones else [],
                "stop_loss_zones": [
                    {
                        "price": zone.price,
                        "confidence": zone.confidence.value,
                        "reason": zone.reason
                    }
                    for zone in analysis.stop_loss_zones
                ] if include_zones else [],
                "take_profit_zones": [
                    {
                        "price": zone.price,
                        "confidence": zone.confidence.value,
                        "reason": zone.reason
                    }
                    for zone in analysis.take_profit_zones
                ] if include_zones else [],
                "risk_reward_ratios": [
                    {
                        "buy_price": ratio.buy_price,
                        "stop_loss_price": ratio.stop_loss_price,
                        "take_profit_price": ratio.take_profit_price,
                        "ratio": ratio.ratio,
                        "quality": ratio.quality
                    }
                    for ratio in analysis.risk_reward_ratios
                ] if include_risk_analysis else []
            }

            logger.info(f"Successfully analyzed {symbol}: {analysis.recommendation.value}")
            return result

        except SymbolNotFoundException as e:
            logger.error(f"Symbol not found: {str(e)}")
            return None
        except InsufficientDataException as e:
            logger.error(f"Insufficient data: {str(e)}")
            return None
        except AnalysisException as e:
            logger.error(f"Analysis failed: {str(e)}")
            return None
        except Exception as e:
            logger.error(f"Unexpected error analyzing {symbol}: {str(e)}")
            return None

    def get_quick_recommendation(self, symbol: str) -> Optional[dict]:
        """
        Get a quick recommendation for a stock.

        Args:
            symbol: Stock symbol

        Returns:
            Quick recommendation as dictionary, or None if failed
        """
        try:
            logger.info(f"Getting quick recommendation for {symbol}")

            recommendation = self.analyze_stock_command.get_quick_recommendation(symbol)

            logger.info(f"Quick recommendation for {symbol}: {recommendation['recommendation']}")
            return recommendation

        except Exception as e:
            logger.error(f"Failed to get quick recommendation for {symbol}: {str(e)}")
            return None

    def compare_stocks(self, symbols: List[str]) -> Optional[dict]:
        """
        Compare multiple stocks.

        Args:
            symbols: List of stock symbols to compare

        Returns:
            Comparison results as dictionary, or None if failed
        """
        try:
            logger.info(f"Comparing stocks: {symbols}")

            comparison = self.analyze_stock_command.compare_stocks(symbols)

            logger.info(f"Successfully compared {len(symbols)} stocks")
            return comparison

        except Exception as e:
            logger.error(f"Failed to compare stocks {symbols}: {str(e)}")
            return None

    def refresh_all_data(self, max_workers: int = 5) -> dict:
        """
        Refresh data for all symbols.

        Args:
            max_workers: Maximum number of concurrent workers

        Returns:
            Refresh results
        """
        try:
            logger.info("Starting bulk data refresh")

            results = self.fetch_data_command.refresh_all_symbols(max_workers=max_workers)

            logger.info(f"Bulk refresh completed: {results['message']}")
            return results

        except Exception as e:
            logger.error(f"Failed to refresh all data: {str(e)}")
            return {"success": 0, "failed": 0, "message": f"Refresh failed: {str(e)}"}

    def get_ml_prediction(self, symbol: str, horizon_days: int = 5) -> Optional[dict]:
        """Get ML-based price prediction for a symbol."""
        try:
            logger.info(f"Getting ML prediction for {symbol}")

            # Get price data
            prices = self.data_service.get_daily_prices(symbol=symbol, days=60)
            if len(prices) < 30:
                logger.warning(f"Insufficient data for ML prediction: {symbol}")
                return None

            # Get technical indicators for context
            indicators = self.indicator_processor.calculate_all_indicators(symbol, prices)
            tech_indicators = []
            for name, values in indicators.items():
                if values and len(values) > 0:
                    tech_indicators.append(TechnicalIndicator(
                        name=name,
                        value=values[-1],
                        signal=SignalType.HOLD,
                        confidence=ConfidenceLevel.MEDIUM,
                        description=f"{name} indicator",
                        timestamp=int(datetime.now().timestamp())
                    ))

            # Get trend prediction
            trend_prediction = self.ml_analytics_service.predict_trend(
                symbol=symbol,
                prices=prices,
                indicators=tech_indicators,
                market_condition=MarketCondition.TRENDING
            )

            # Get price prediction
            price_prediction = self.ml_analytics_service.predict_price_target(
                symbol=symbol,
                prices=prices,
                trend_prediction=trend_prediction,
                horizon_days=horizon_days
            )

            result = {
                "symbol": symbol,
                "current_price": prices[-1].close_price,
                "predicted_price": price_prediction.predicted_price,
                "prediction_horizon_days": horizon_days,
                "confidence": price_prediction.confidence,
                "trend_direction": trend_prediction.direction.value,
                "trend_strength": trend_prediction.strength,
                "key_levels": trend_prediction.key_levels,
                "model_used": price_prediction.model_used
            }

            logger.info(f"ML prediction for {symbol}: {price_prediction.predicted_price:.2f}")
            return result

        except Exception as e:
            logger.error(f"Failed to get ML prediction for {symbol}: {str(e)}")
            return None

    def run_backtest(self, symbol: str, strategy_params: Optional[dict] = None) -> Optional[dict]:
        """Run backtest for a symbol with given strategy parameters."""
        try:
            logger.info(f"Running backtest for {symbol}")

            # Get historical data
            prices = self.data_service.get_daily_prices(symbol=symbol, days=180)
            if len(prices) < 60:
                logger.warning(f"Insufficient data for backtesting: {symbol}")
                return None

            # Generate trading signals (simplified strategy)
            signals = []
            base_date = datetime.fromtimestamp(prices[30].timestamp)

            # Simple moving average crossover strategy
            ma_short = self.indicator_processor.calculate_moving_averages(symbol, prices, [10])
            ma_long = self.indicator_processor.calculate_moving_averages(symbol, prices, [30])

            if ma_short.get("sma10") and ma_long.get("sma30"):
                short_ma = ma_short["sma10"]
                long_ma = ma_long["sma30"]

                for i in range(30, len(prices) - 10):
                    if (i < len(short_ma) and i < len(long_ma) and
                        short_ma[i] and long_ma[i]):

                        signal_date = datetime.fromtimestamp(prices[i].timestamp)

                        # Buy signal: short MA crosses above long MA
                        if (i > 0 and short_ma[i] > long_ma[i] and
                            short_ma[i-1] <= long_ma[i-1]):
                            signals.append((signal_date, SignalType.BUY, 0.8))

                        # Sell signal: short MA crosses below long MA
                        elif (i > 0 and short_ma[i] < long_ma[i] and
                              short_ma[i-1] >= long_ma[i-1]):
                            signals.append((signal_date, SignalType.SELL, 0.8))

            if not signals:
                logger.warning(f"No trading signals generated for {symbol}")
                return None

            # Run backtest
            backtest_result = self.backtesting_service.backtest_strategy(
                symbol=symbol,
                prices=prices,
                signals=signals,
                stop_loss_percent=strategy_params.get("stop_loss", 0.05) if strategy_params else 0.05,
                take_profit_percent=strategy_params.get("take_profit", 0.10) if strategy_params else 0.10
            )

            result = {
                "symbol": symbol,
                "total_return_percent": backtest_result.total_return_percent,
                "total_trades": backtest_result.total_trades,
                "win_rate": backtest_result.win_rate,
                "sharpe_ratio": backtest_result.sharpe_ratio,
                "max_drawdown_percent": backtest_result.max_drawdown_percent,
                "final_capital": backtest_result.final_capital,
                "strategy": "MA Crossover",
                "signals_count": len(signals)
            }

            logger.info(f"Backtest for {symbol}: {backtest_result.total_return_percent:.2f}% return")
            return result

        except Exception as e:
            logger.error(f"Failed to run backtest for {symbol}: {str(e)}")
            return None

    def get_cache_stats(self) -> dict:
        """Get cache statistics."""
        try:
            return self.cache_manager.get_cache_stats()
        except Exception as e:
            logger.error(f"Failed to get cache stats: {str(e)}")
            return {}

    def cleanup_cache(self) -> dict:
        """Clean up expired cache entries."""
        try:
            cleaned_count = self.cache_manager.cleanup_expired()
            return {"cleaned_entries": cleaned_count}
        except Exception as e:
            logger.error(f"Failed to cleanup cache: {str(e)}")
            return {"cleaned_entries": 0}

    def run_batch_analysis(
        self,
        symbols: Optional[List[str]] = None,
        test_mode: bool = False,
        export_results: bool = True
    ) -> Optional[dict]:
        """
        Run batch analysis on multiple symbols.

        Args:
            symbols: List of symbols to analyze (None for all symbols)
            test_mode: If True, only analyze a subset for testing
            export_results: Whether to export results to files

        Returns:
            Batch analysis results
        """
        try:
            logger.info("Starting batch analysis")

            if symbols:
                # Analyze specific symbols
                results = self.batch_analysis_use_case.analyze_symbol_list(
                    symbols=symbols,
                    export_results=export_results
                )
            else:
                # Analyze all symbols
                results = self.batch_analysis_use_case.analyze_all_symbols(
                    test_mode=test_mode,
                    export_results=export_results
                )

            logger.info(f"Batch analysis completed: {results['successful_analyses']} successful")
            return results

        except Exception as e:
            logger.error(f"Batch analysis failed: {str(e)}")
            return None


def main():
    """Main function demonstrating the refactored application."""
    try:
        # Initialize application
        app = StockPalApplication()

        # Example usage
        test_symbols = ["VIC", "VHM", "HPG", "TCB", "VCB"]

        print("=== StockPal Refactored Application Demo ===\n")

        # 1. Fetch data for test symbols
        print("1. Fetching data for test symbols...")
        for symbol in test_symbols[:2]:  # Test with first 2 symbols
            success = app.fetch_stock_data(symbol, days=100)
            print(f"   {symbol}: {'✓' if success else '✗'}")

        # 2. Analyze a single stock
        print(f"\n2. Analyzing stock: {test_symbols[0]}")
        analysis = app.analyze_stock(test_symbols[0], days_back=90)
        if analysis:
            print(f"   Recommendation: {analysis['recommendation']}")
            print(f"   Trend: {analysis['trend']['direction']} ({analysis['trend']['strength']:.1%})")
            print(f"   Confidence: {analysis['confidence_score']:.1f}%")

        # 3. Get quick recommendations
        print(f"\n3. Quick recommendations:")
        for symbol in test_symbols[:3]:
            rec = app.get_quick_recommendation(symbol)
            if rec:
                print(f"   {symbol}: {rec['recommendation']} (Confidence: {rec['confidence_score']:.1f}%)")

        # 4. Compare stocks
        print(f"\n4. Comparing stocks: {test_symbols[:3]}")
        comparison = app.compare_stocks(test_symbols[:3])
        if comparison:
            buy_recs = comparison.get('rankings', {}).get('buy_recommendations', [])
            print(f"   Buy recommendations: {buy_recs}")

        print("\n=== Demo completed successfully ===")

    except Exception as e:
        logger.error(f"Application demo failed: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    main()
