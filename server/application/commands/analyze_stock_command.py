"""
Command handler for stock analysis operations.

This module provides command handlers for performing stock analysis.
"""

import logging
from typing import List, Optional
from datetime import datetime

from shared.models.stock_models import (
    StockAnalysis, AnalysisRequest, IndicatorConfig, SignalType
)
from shared.exceptions.stock_exceptions import (
    AnalysisException, SymbolNotFoundException, InsufficientDataException
)
from shared.utils.validation import validate_symbol, validate_days_back
from core.services.analysis_service import AnalysisService


logger = logging.getLogger(__name__)


class AnalyzeStockCommand:
    """Command for analyzing stocks."""

    def __init__(self, analysis_service: AnalysisService):
        """
        Initialize the command handler.

        Args:
            analysis_service: Service for analysis operations
        """
        self._analysis_service = analysis_service
        self._logger = logging.getLogger(__name__)

    def analyze_single_stock(
        self,
        symbol: str,
        days_back: int = 365,
        end_date: Optional[datetime] = None,
        include_zones: bool = True,
        include_risk_analysis: bool = True,
        custom_indicators: Optional[List[IndicatorConfig]] = None
    ) -> StockAnalysis:
        """
        Analyze a single stock.

        Args:
            symbol: Stock symbol to analyze
            days_back: Number of days of historical data to use
            end_date: End date for analysis (defaults to now)
            include_zones: Whether to include trading zones
            include_risk_analysis: Whether to include risk-reward analysis
            custom_indicators: Custom indicator configurations

        Returns:
            Complete stock analysis

        Raises:
            AnalysisException: If analysis fails
            SymbolNotFoundException: If symbol is not found
            InsufficientDataException: If not enough data is available
        """
        try:
            symbol = validate_symbol(symbol)
            days_back = validate_days_back(days_back)

            self._logger.info(f"Executing analyze single stock command for {symbol}")

            # Create analysis request
            request = AnalysisRequest(
                symbol=symbol,
                end_date=end_date,
                days_back=days_back,
                indicators=custom_indicators,
                include_zones=include_zones,
                include_risk_analysis=include_risk_analysis
            )

            # Perform analysis
            analysis = self._analysis_service.analyze_stock(request)

            self._logger.info(f"Successfully completed analysis for {symbol}")
            return analysis

        except Exception as e:
            if isinstance(e, (AnalysisException, SymbolNotFoundException, InsufficientDataException)):
                raise

            error_msg = f"Failed to execute analyze single stock command for {symbol}: {str(e)}"
            self._logger.error(error_msg, exc_info=True)
            raise AnalysisException(error_msg)

    def analyze_multiple_stocks(
        self,
        symbols: List[str],
        days_back: int = 365,
        end_date: Optional[datetime] = None,
        include_zones: bool = True,
        include_risk_analysis: bool = True
    ) -> List[StockAnalysis]:
        """
        Analyze multiple stocks.

        Args:
            symbols: List of stock symbols to analyze
            days_back: Number of days of historical data to use
            end_date: End date for analysis (defaults to now)
            include_zones: Whether to include trading zones
            include_risk_analysis: Whether to include risk-reward analysis

        Returns:
            List of stock analyses

        Raises:
            AnalysisException: If analysis fails
        """
        try:
            if not symbols:
                raise AnalysisException("No symbols provided for analysis")

            # Validate all symbols first
            validated_symbols = [validate_symbol(symbol) for symbol in symbols]
            days_back = validate_days_back(days_back)

            self._logger.info(f"Executing analyze multiple stocks command for {len(validated_symbols)} symbols")

            analyses = []
            failed_symbols = []

            for symbol in validated_symbols:
                try:
                    analysis = self.analyze_single_stock(
                        symbol=symbol,
                        days_back=days_back,
                        end_date=end_date,
                        include_zones=include_zones,
                        include_risk_analysis=include_risk_analysis
                    )
                    analyses.append(analysis)

                except Exception as symbol_error:
                    self._logger.warning(f"Failed to analyze {symbol}: {str(symbol_error)}")
                    failed_symbols.append(symbol)

            if failed_symbols:
                self._logger.warning(f"Failed to analyze {len(failed_symbols)} symbols: {failed_symbols}")

            self._logger.info(f"Successfully analyzed {len(analyses)} out of {len(validated_symbols)} symbols")
            return analyses

        except Exception as e:
            if isinstance(e, AnalysisException):
                raise

            error_msg = f"Failed to execute analyze multiple stocks command: {str(e)}"
            self._logger.error(error_msg, exc_info=True)
            raise AnalysisException(error_msg)

    def get_quick_recommendation(
        self,
        symbol: str,
        days_back: int = 90
    ) -> dict:
        """
        Get a quick recommendation for a stock with minimal analysis.

        Args:
            symbol: Stock symbol
            days_back: Number of days of data to use (default: 90)

        Returns:
            Dictionary with quick recommendation

        Raises:
            AnalysisException: If analysis fails
        """
        try:
            symbol = validate_symbol(symbol)
            days_back = validate_days_back(days_back)

            self._logger.info(f"Executing quick recommendation command for {symbol}")

            # Perform lightweight analysis
            analysis = self.analyze_single_stock(
                symbol=symbol,
                days_back=days_back,
                include_zones=False,
                include_risk_analysis=False
            )

            # Extract key information for quick recommendation
            recommendation = {
                "symbol": symbol,
                "current_price": analysis.current_price,
                "price_change_percent": analysis.price_change_percent,
                "recommendation": analysis.recommendation.value,
                "trend_direction": analysis.trend_analysis.direction.value,
                "trend_strength": analysis.trend_analysis.strength,
                "confidence_score": analysis.confidence_score,
                "market_condition": analysis.market_condition.condition,
                "key_indicators": {}
            }

            # Add key technical indicators
            for indicator in analysis.technical_indicators:
                if indicator.name in ["RSI", "MACD", "SMA20", "SMA50"]:
                    recommendation["key_indicators"][indicator.name] = {
                        "value": indicator.value,
                        "signal": indicator.signal.value
                    }

            self._logger.info(f"Generated quick recommendation for {symbol}: {analysis.recommendation.value}")
            return recommendation

        except Exception as e:
            if isinstance(e, AnalysisException):
                raise

            error_msg = f"Failed to execute quick recommendation command for {symbol}: {str(e)}"
            self._logger.error(error_msg, exc_info=True)
            raise AnalysisException(error_msg)

    def compare_stocks(
        self,
        symbols: List[str],
        days_back: int = 365,
        comparison_criteria: Optional[List[str]] = None
    ) -> dict:
        """
        Compare multiple stocks based on various criteria.

        Args:
            symbols: List of stock symbols to compare
            days_back: Number of days of historical data to use
            comparison_criteria: List of criteria to compare (e.g., 'trend', 'volatility', 'rsi')

        Returns:
            Dictionary with comparison results

        Raises:
            AnalysisException: If comparison fails
        """
        try:
            if len(symbols) < 2:
                raise AnalysisException("At least 2 symbols required for comparison")

            validated_symbols = [validate_symbol(symbol) for symbol in symbols]
            days_back = validate_days_back(days_back)

            self._logger.info(f"Executing stock comparison command for {len(validated_symbols)} symbols")

            # Analyze all stocks
            analyses = self.analyze_multiple_stocks(
                symbols=validated_symbols,
                days_back=days_back,
                include_zones=False,
                include_risk_analysis=False
            )

            if not analyses:
                raise AnalysisException("No successful analyses for comparison")

            # Default comparison criteria
            if not comparison_criteria:
                comparison_criteria = ["trend", "volatility", "rsi", "recommendation"]

            # Build comparison results
            comparison = {
                "symbols": [analysis.symbol for analysis in analyses],
                "comparison_date": datetime.now().isoformat(),
                "criteria": {},
                "rankings": {}
            }

            # Compare based on each criterion
            for criterion in comparison_criteria:
                if criterion == "trend":
                    comparison["criteria"]["trend"] = {
                        analysis.symbol: {
                            "direction": analysis.trend_analysis.direction.value,
                            "strength": analysis.trend_analysis.strength
                        }
                        for analysis in analyses
                    }
                elif criterion == "volatility":
                    comparison["criteria"]["volatility"] = {
                        analysis.symbol: analysis.market_condition.volatility
                        for analysis in analyses
                    }
                elif criterion == "rsi":
                    rsi_values = {}
                    for analysis in analyses:
                        rsi_indicator = next(
                            (ind for ind in analysis.technical_indicators if ind.name == "RSI"),
                            None
                        )
                        rsi_values[analysis.symbol] = rsi_indicator.value if rsi_indicator else None
                    comparison["criteria"]["rsi"] = rsi_values
                elif criterion == "recommendation":
                    comparison["criteria"]["recommendation"] = {
                        analysis.symbol: analysis.recommendation.value
                        for analysis in analyses
                    }

            # Generate rankings (simplified)
            buy_recommendations = [
                analysis.symbol for analysis in analyses
                if analysis.recommendation == SignalType.BUY
            ]
            comparison["rankings"]["buy_recommendations"] = buy_recommendations

            self._logger.info(f"Completed stock comparison for {len(analyses)} symbols")
            return comparison

        except Exception as e:
            if isinstance(e, AnalysisException):
                raise

            error_msg = f"Failed to execute stock comparison command: {str(e)}"
            self._logger.error(error_msg, exc_info=True)
            raise AnalysisException(error_msg)
