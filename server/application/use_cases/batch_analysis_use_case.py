"""
Batch analysis use case for processing multiple stocks.

This use case handles batch processing of stock analysis, including
data fetching, analysis execution, and result export for multiple symbols.
"""

import logging
from concurrent.futures import ThreadPoolExecutor, as_completed
from datetime import datetime
from typing import List, Dict, Any, Optional

from shared.models.stock_models import AnalysisRequest, StockAnalysis
from shared.exceptions.stock_exceptions import AnalysisException
from shared.utils.validation import validate_symbol
from core.services.analysis_service import AnalysisService
from core.services.export_service import ExportService
from infrastructure.repositories.symbol_repository import SymbolRepositoryInterface


logger = logging.getLogger(__name__)


class BatchAnalysisUseCase:
    """Use case for batch analysis of multiple stocks."""

    def __init__(
        self,
        analysis_service: AnalysisService,
        export_service: ExportService,
        symbol_repository: SymbolRepositoryInterface,
        max_workers: int = 10
    ):
        """
        Initialize the batch analysis use case.

        Args:
            analysis_service: Service for stock analysis
            export_service: Service for exporting results
            symbol_repository: Repository for symbol data
            max_workers: Maximum number of concurrent workers
        """
        self._analysis_service = analysis_service
        self._export_service = export_service
        self._symbol_repository = symbol_repository
        self._max_workers = max_workers
        self._logger = logger

    def analyze_all_symbols(
        self,
        days_back: int = 200,
        end_date: Optional[datetime] = None,
        include_zones: bool = True,
        include_risk_analysis: bool = True,
        export_results: bool = True,
        test_mode: bool = False
    ) -> Dict[str, Any]:
        """
        Analyze all available symbols in the database.

        Args:
            days_back: Number of days of historical data to analyze
            end_date: End date for analysis (None for current date)
            include_zones: Whether to calculate trading zones
            include_risk_analysis: Whether to perform risk analysis
            export_results: Whether to export results to files
            test_mode: If True, only analyze a subset of symbols for testing

        Returns:
            Dictionary containing analysis results and statistics
        """
        try:
            self._logger.info("Starting batch analysis of all symbols")

            # Get all symbols from database
            all_symbols = self._symbol_repository.get_all_symbols()
            
            if not all_symbols:
                raise AnalysisException("No symbols found in database")

            # Filter symbols for test mode
            if test_mode:
                symbols_to_analyze = ["HPG", "TCB", "VNM", "VIC", "VHM"]
                symbols_to_analyze = [s for s in symbols_to_analyze if s in [sym.code for sym in all_symbols]]
            else:
                symbols_to_analyze = [symbol.code for symbol in all_symbols if symbol.code != "VNINDEX"]

            self._logger.info(f"Analyzing {len(symbols_to_analyze)} symbols")

            # Perform batch analysis
            results = self._execute_batch_analysis(
                symbols_to_analyze,
                days_back=days_back,
                end_date=end_date,
                include_zones=include_zones,
                include_risk_analysis=include_risk_analysis
            )

            # Export results if requested
            if export_results:
                self._export_batch_results(results, end_date)

            # Generate statistics
            stats = self._generate_analysis_statistics(results)

            return {
                "analysis_results": results,
                "statistics": stats,
                "total_symbols": len(symbols_to_analyze),
                "successful_analyses": len([r for r in results if r.get("success", False)]),
                "analysis_date": end_date or datetime.now()
            }

        except Exception as e:
            self._logger.error(f"Batch analysis failed: {str(e)}")
            raise AnalysisException(f"Batch analysis failed: {str(e)}")

    def analyze_symbol_list(
        self,
        symbols: List[str],
        days_back: int = 200,
        end_date: Optional[datetime] = None,
        include_zones: bool = True,
        include_risk_analysis: bool = True,
        export_results: bool = True
    ) -> Dict[str, Any]:
        """
        Analyze a specific list of symbols.

        Args:
            symbols: List of stock symbols to analyze
            days_back: Number of days of historical data to analyze
            end_date: End date for analysis (None for current date)
            include_zones: Whether to calculate trading zones
            include_risk_analysis: Whether to perform risk analysis
            export_results: Whether to export results to files

        Returns:
            Dictionary containing analysis results and statistics
        """
        try:
            self._logger.info(f"Starting batch analysis of {len(symbols)} specified symbols")

            # Validate symbols
            validated_symbols = []
            for symbol in symbols:
                try:
                    validated_symbol = validate_symbol(symbol)
                    validated_symbols.append(validated_symbol)
                except Exception as e:
                    self._logger.warning(f"Invalid symbol {symbol}: {str(e)}")

            if not validated_symbols:
                raise AnalysisException("No valid symbols provided")

            # Perform batch analysis
            results = self._execute_batch_analysis(
                validated_symbols,
                days_back=days_back,
                end_date=end_date,
                include_zones=include_zones,
                include_risk_analysis=include_risk_analysis
            )

            # Export results if requested
            if export_results:
                self._export_batch_results(results, end_date)

            # Generate statistics
            stats = self._generate_analysis_statistics(results)

            return {
                "analysis_results": results,
                "statistics": stats,
                "total_symbols": len(validated_symbols),
                "successful_analyses": len([r for r in results if r.get("success", False)]),
                "analysis_date": end_date or datetime.now()
            }

        except Exception as e:
            self._logger.error(f"Symbol list analysis failed: {str(e)}")
            raise AnalysisException(f"Symbol list analysis failed: {str(e)}")

    def _execute_batch_analysis(
        self,
        symbols: List[str],
        days_back: int,
        end_date: Optional[datetime],
        include_zones: bool,
        include_risk_analysis: bool
    ) -> List[Dict[str, Any]]:
        """Execute batch analysis using thread pool."""
        results = []
        
        # Limit max workers to avoid overwhelming the system
        max_workers = min(self._max_workers, len(symbols))
        
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # Submit all analysis tasks
            future_to_symbol = {
                executor.submit(
                    self._analyze_single_symbol,
                    symbol,
                    days_back,
                    end_date,
                    include_zones,
                    include_risk_analysis
                ): symbol
                for symbol in symbols
            }

            # Process completed tasks
            for future in as_completed(future_to_symbol):
                symbol = future_to_symbol[future]
                try:
                    result = future.result()
                    results.append(result)
                    self._logger.info(f"Successfully analyzed {symbol}")
                except Exception as e:
                    error_result = {
                        "symbol": symbol,
                        "success": False,
                        "error": str(e),
                        "analysis_date": (end_date or datetime.now()).strftime("%Y-%m-%d")
                    }
                    results.append(error_result)
                    self._logger.error(f"Failed to analyze {symbol}: {str(e)}")

        return results

    def _analyze_single_symbol(
        self,
        symbol: str,
        days_back: int,
        end_date: Optional[datetime],
        include_zones: bool,
        include_risk_analysis: bool
    ) -> Dict[str, Any]:
        """Analyze a single symbol and return formatted result."""
        try:
            # Create analysis request
            request = AnalysisRequest(
                symbol=symbol,
                end_date=end_date,
                days_back=days_back,
                include_zones=include_zones,
                include_risk_analysis=include_risk_analysis
            )

            # Perform analysis
            analysis = self._analysis_service.analyze_stock(request)

            # Convert to web-compatible format
            result = self._convert_analysis_to_batch_format(analysis)
            result["success"] = True

            return result

        except Exception as e:
            raise AnalysisException(f"Analysis failed for {symbol}: {str(e)}")

    def _convert_analysis_to_batch_format(self, analysis: StockAnalysis) -> Dict[str, Any]:
        """Convert StockAnalysis to batch result format."""
        return {
            "symbol": analysis.symbol,
            "current_price": analysis.current_price,
            "trend_change": analysis.price_change,
            "trend_change_percent": analysis.price_change_percent,
            "trend_direction": analysis.trend_analysis.direction.value,
            "trend_strength": analysis.trend_analysis.strength,
            "trend_confidence": f"{analysis.trend_analysis.confidence.value}",
            "market_condition": analysis.market_condition.condition,
            "recommendation": analysis.recommendation.value,
            "confidence_score": analysis.confidence_score,
            "analysis_date": analysis.analysis_date.strftime("%Y-%m-%d") if analysis.analysis_date else None,
            "buy_zones": [
                {
                    "price": zone.price,
                    "confidence": zone.confidence.value,
                    "reason": zone.reason
                }
                for zone in analysis.buy_zones
            ],
            "stop_loss_zones": [
                {
                    "price": zone.price,
                    "confidence": zone.confidence.value,
                    "reason": zone.reason
                }
                for zone in analysis.stop_loss_zones
            ],
            "take_profit_zones": [
                {
                    "price": zone.price,
                    "confidence": zone.confidence.value,
                    "reason": zone.reason
                }
                for zone in analysis.take_profit_zones
            ],
            "risk_reward_ratios": [
                {
                    "buy_price": rr.buy_price,
                    "stop_loss_price": rr.stop_loss_price,
                    "take_profit_price": rr.take_profit_price,
                    "ratio": rr.ratio,
                    "quality": rr.quality
                }
                for rr in analysis.risk_reward_ratios
            ],
            "technical_summary": analysis.technical_summary
        }

    def _export_batch_results(self, results: List[Dict[str, Any]], end_date: Optional[datetime]):
        """Export batch analysis results to files."""
        try:
            # Filter successful results
            successful_results = [r for r in results if r.get("success", False)]
            
            if not successful_results:
                self._logger.warning("No successful results to export")
                return

            # Export to JavaScript file for web consumption
            self._export_service.export_batch_analysis_to_js(successful_results, end_date)

            # Export individual analysis results
            for result in successful_results:
                symbol = result["symbol"]
                
                # Create a mock StockAnalysis object for export
                # This is a simplified approach - in a real implementation,
                # you might want to store the full analysis objects
                self._export_service.export_analysis_to_web(result, symbol)

            self._logger.info(f"Exported {len(successful_results)} analysis results")

        except Exception as e:
            self._logger.error(f"Failed to export batch results: {str(e)}")

    def _generate_analysis_statistics(self, results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Generate statistics from analysis results."""
        successful_results = [r for r in results if r.get("success", False)]
        
        if not successful_results:
            return {"total": len(results), "successful": 0, "failed": len(results)}

        # Count recommendations
        recommendations = {}
        trend_directions = {}
        
        for result in successful_results:
            rec = result.get("recommendation", "NEUTRAL")
            recommendations[rec] = recommendations.get(rec, 0) + 1
            
            trend = result.get("trend_direction", "SIDEWAYS")
            trend_directions[trend] = trend_directions.get(trend, 0) + 1

        return {
            "total": len(results),
            "successful": len(successful_results),
            "failed": len(results) - len(successful_results),
            "recommendations": recommendations,
            "trend_directions": trend_directions,
            "success_rate": len(successful_results) / len(results) * 100 if results else 0
        }
