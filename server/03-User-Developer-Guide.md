# StockPal User & Developer Guide

## Table of Contents

1. [Quick Start](#quick-start)
2. [Installation & Setup](#installation--setup)
3. [User Guide](#user-guide)
4. [Developer Guide](#developer-guide)
5. [Configuration](#configuration)
6. [Troubleshooting](#troubleshooting)
7. [Contributing](#contributing)

## Quick Start

### For Users (5-minute setup)

```bash
# 1. Clone the repository
git clone <repository-url>
cd stockpal

# 2. Install dependencies
pip install -r requirements.txt

# 3. Run basic analysis
cd server
python -m stockpal.examples.basic_analysis VCB
```

### For Developers (10-minute setup)

```bash
# 1. Clone and setup
git clone <repository-url>
cd stockpal

# 2. Create virtual environment
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# 3. Install development dependencies
pip install -r requirements-dev.txt

# 4. Run tests
cd server
python -m pytest tests/

# 5. Start development server
python -m stockpal.web.app
```

## Installation & Setup

### System Requirements

- **Python**: 3.7 or higher
- **Memory**: Minimum 4GB RAM (8GB recommended)
- **Storage**: 1GB free space for cache and logs
- **Network**: Internet connection for data fetching

### Installation Steps

#### 1. Environment Setup

```bash
# Create virtual environment
python -m venv stockpal-env

# Activate environment
# On Linux/Mac:
source stockpal-env/bin/activate
# On Windows:
stockpal-env\Scripts\activate
```

#### 2. Install Dependencies

```bash
# Core dependencies
pip install numpy pandas requests dataclasses

# Optional dependencies for enhanced features
pip install matplotlib flask ta-lib

# Development dependencies (for contributors)
pip install pytest pylint black mypy
```

#### 3. Configuration

```bash
# Create configuration directory
mkdir -p server/config

# Copy example configuration
cp server/config/config.example.py server/config/config.py

# Edit configuration as needed
nano server/config/config.py
```

#### 4. Verify Installation

```bash
cd server
python -c "from stockpal.core import Constants; print('Installation successful!')"
```

## User Guide

### Basic Usage

#### 1. Analyzing a Single Stock

```python
from stockpal.data import DataScraper
from stockpal.analysis import TrendPredictor

# Fetch data for VCB stock
scraper = DataScraper("VCB", provider="ssi")
prices = scraper.fetch_prices()

# Analyze trends
predictor = TrendPredictor("VCB", prices)
analysis = predictor.analyze_trend()

print(f"Trend: {analysis['trend']}")
print(f"Confidence: {analysis['confidence']:.2%}")
```

#### 2. Getting Trading Signals

```python
from stockpal.indicator import RelativeStrengthIndex

# Calculate RSI
rsi = RelativeStrengthIndex("VCB", prices, period=14)
signals = rsi.get_signals()

for signal in signals:
    print(f"{signal['timestamp']}: {signal['signal_type']} - {signal['reason']}")
```

#### 3. Multiple Indicator Analysis

```python
from stockpal.indicator import RelativeStrengthIndex, MACD, BollingerBands

# Initialize indicators
rsi = RelativeStrengthIndex("VCB", prices)
macd = MACD("VCB", prices)
bb = BollingerBands("VCB", prices)

# Get recommendations
print(f"RSI: {rsi.get_recommendation()}")
print(f"MACD: {macd.get_recommendation()}")
print(f"Bollinger Bands: {bb.get_recommendation()}")
```

### Web Interface Usage

#### 1. Starting the Web Interface

```bash
cd web_test
python -m http.server 8000
# Open browser to http://localhost:8000
```

#### 2. Interface Features

- **Stock Selection**: Choose from watchlists or search symbols
- **Chart Visualization**: Interactive price charts with indicators
- **Technical Analysis**: Real-time indicator calculations
- **Signal Dashboard**: Trading signals with confidence levels
- **Watchlist Management**: Create and manage stock watchlists

#### 3. Keyboard Shortcuts

- `Ctrl + S`: Save current analysis
- `Ctrl + R`: Refresh data
- `Ctrl + F`: Search stocks
- `Esc`: Close dialogs

### Advanced Features

#### 1. Custom Indicator Parameters

```python
# Custom RSI with different period
rsi_custom = RelativeStrengthIndex("VCB", prices, period=21)

# Custom MACD parameters
macd_custom = MACD("VCB", prices, fast_period=8, slow_period=21, signal_period=5)

# Custom Bollinger Bands
bb_custom = BollingerBands("VCB", prices, period=20, std_dev=2.5)
```

#### 2. Backtesting

```python
from stockpal.backtesting import BacktestEngine

# Setup backtesting
backtest = BacktestEngine("VCB", prices, initial_capital=100000)

# Add strategy
strategy = backtest.add_rsi_strategy(period=14, overbought=70, oversold=30)

# Run backtest
results = backtest.run()
print(f"Total Return: {results['total_return']:.2%}")
print(f"Win Rate: {results['win_rate']:.2%}")
```

#### 3. Data Export

```python
# Export to Excel
from stockpal.utils import ExcelExporter

exporter = ExcelExporter()
exporter.export_analysis("VCB", analysis, "vcb_analysis.xlsx")

# Export to CSV
import pandas as pd
df = pd.DataFrame(prices)
df.to_csv("vcb_prices.csv", index=False)
```

## Developer Guide

### Project Structure

```
stockpal/
├── server/                 # Backend Python code
│   ├── stockpal/          # Main package
│   │   ├── core/          # Core utilities
│   │   ├── data/          # Data providers
│   │   ├── indicator/     # Technical indicators
│   │   ├── analysis/      # Analysis services
│   │   └── web/           # Web API (planned)
│   ├── tests/             # Test suite
│   └── config/            # Configuration files
├── web_test/              # Frontend interface
├── docs/                  # Documentation
└── requirements.txt       # Dependencies
```

### Development Workflow

#### 1. Setting Up Development Environment

```bash
# Clone repository
git clone <repository-url>
cd stockpal

# Setup development environment
python -m venv dev-env
source dev-env/bin/activate

# Install development dependencies
pip install -r requirements-dev.txt

# Setup pre-commit hooks
pre-commit install
```

#### 2. Code Style Guidelines

- **PEP 8**: Follow Python style guidelines
- **Type Hints**: Use type annotations for all functions
- **Docstrings**: Document all classes and methods
- **Imports**: Organize imports (stdlib, third-party, local)

```python
# Example of proper code style
from typing import List, Optional
import logging

from stockpal.core import PriceData
from .base import BaseIndicator

class ExampleIndicator(BaseIndicator):
    """
    Example indicator implementation.
    
    Args:
        symbol: Stock symbol
        prices: Historical price data
        period: Calculation period
    """
    
    def __init__(self, symbol: str, prices: List[PriceData], period: int = 14):
        super().__init__(symbol, prices, period=period)
        self.logger = logging.getLogger(__name__)
```

#### 3. Testing Guidelines

```python
# Example test structure
import pytest
from stockpal.indicator import RelativeStrengthIndex
from tests.fixtures import sample_price_data

class TestRSI:
    def test_rsi_calculation(self):
        """Test RSI calculation with known values."""
        rsi = RelativeStrengthIndex("TEST", sample_price_data)
        values = rsi.calculate()
        
        assert len(values) == len(sample_price_data)
        assert all(0 <= v <= 100 for v in values if v is not None)
    
    def test_rsi_signals(self):
        """Test RSI signal generation."""
        rsi = RelativeStrengthIndex("TEST", sample_price_data)
        signals = rsi.get_signals()
        
        assert isinstance(signals, list)
        for signal in signals:
            assert 'signal_type' in signal
            assert signal['signal_type'] in ['buy', 'sell', 'hold']
```

#### 4. Adding New Indicators

```python
# 1. Create new indicator file
# server/stockpal/indicator/my_indicator.py

from typing import List, Dict, Any
from stockpal.core import PriceData
from .base import BaseIndicator

class MyIndicator(BaseIndicator):
    """Custom indicator implementation."""
    
    def __init__(self, symbol: str, prices: List[PriceData], period: int = 14):
        super().__init__(symbol, prices, period=period)
    
    def calculate(self) -> List[float]:
        """Implement calculation logic."""
        # Your calculation here
        pass
    
    def get_signals(self) -> List[Dict[str, Any]]:
        """Implement signal generation."""
        # Your signal logic here
        pass

# 2. Add to __init__.py
# server/stockpal/indicator/__init__.py
from .my_indicator import MyIndicator

# 3. Write tests
# tests/test_my_indicator.py
```

#### 5. Adding New Data Providers

```python
# 1. Create provider scraper
# server/stockpal/data/my_provider_scraper.py

from typing import override
from stockpal.core import Scraper, PriceData

class MyProviderScraper(Scraper):
    """Scraper for My Provider API."""
    
    @override
    def prices(self, timeframe_in_minute: bool = False) -> List[PriceData]:
        """Fetch price data from My Provider."""
        # Implementation here
        pass

# 2. Update DataScraper factory
# server/stockpal/data/data_scraper.py
from .my_provider_scraper import MyProviderScraper

class DataScraper:
    def __init__(self, symbol: str, provider: str = "ssi"):
        # Add new provider option
        if provider == "myprovider":
            self._scraper = MyProviderScraper(symbol)
```

### Debugging and Logging

#### 1. Enable Debug Logging

```python
from stockpal.core.logging_config import StockPalLogger

# Initialize with debug level
StockPalLogger.initialize(log_level="DEBUG")

# Get logger for specific component
logger = StockPalLogger.get_indicator_logger("rsi", "VCB")
logger.debug("Debug message")
```

#### 2. Log File Locations

```
server/db/logs/
├── stockpal.log           # General application log
├── VCB/                   # Symbol-specific logs
│   ├── rsi.log           # RSI indicator logs
│   ├── macd.log          # MACD indicator logs
│   └── analysis.log      # Analysis logs
└── data_ssi.log          # Data provider logs
```

#### 3. Performance Profiling

```python
import cProfile
import pstats

# Profile indicator calculation
def profile_rsi():
    rsi = RelativeStrengthIndex("VCB", prices)
    return rsi.calculate()

# Run profiler
cProfile.run('profile_rsi()', 'rsi_profile.stats')

# Analyze results
stats = pstats.Stats('rsi_profile.stats')
stats.sort_stats('cumulative').print_stats(10)
```

## Configuration

### Environment Variables

```bash
# Data provider settings
STOCKPAL_DEFAULT_PROVIDER=ssi
STOCKPAL_CACHE_TTL=86400

# Logging settings
STOCKPAL_LOG_LEVEL=INFO
STOCKPAL_LOG_DIR=/path/to/logs

# API settings
STOCKPAL_REQUEST_TIMEOUT=30
STOCKPAL_MAX_RETRIES=3
```

### Configuration File

```python
# server/config/config.py

class Config:
    # Data provider settings
    DEFAULT_PROVIDER = "ssi"
    CACHE_TTL_HOURS = 24
    
    # Indicator defaults
    RSI_PERIOD = 14
    MACD_FAST = 12
    MACD_SLOW = 26
    MACD_SIGNAL = 9
    
    # Risk management
    DEFAULT_RISK_PERCENTAGE = 2.0
    MAX_POSITION_SIZE = 0.1  # 10% of portfolio
    
    # Performance settings
    MAX_CONCURRENT_REQUESTS = 5
    REQUEST_TIMEOUT = 30
```

## Troubleshooting

### Common Issues

#### 1. Import Errors

```bash
# Error: ModuleNotFoundError: No module named 'stockpal'
# Solution: Ensure you're in the server directory
cd server
python -m stockpal.examples.basic_analysis VCB
```

#### 2. Data Fetching Failures

```python
# Error: DataFetchError: Failed to fetch data
# Solution: Check network connection and provider status
from stockpal.data import DataScraper

scraper = DataScraper("VCB", provider="vietstock")  # Try different provider
```

#### 3. Insufficient Data Errors

```python
# Error: InsufficientDataError: Need at least 14 periods
# Solution: Use shorter period or fetch more historical data
rsi = RelativeStrengthIndex("VCB", prices, period=7)  # Shorter period
```

#### 4. Performance Issues

```python
# Issue: Slow indicator calculations
# Solution: Use vectorized operations and caching
import numpy as np

# Use NumPy for calculations
prices_array = np.array([p.close_price for p in prices])
```

### Getting Help

1. **Documentation**: Check API documentation and examples
2. **Logs**: Review log files for detailed error information
3. **Tests**: Run test suite to verify installation
4. **Issues**: Report bugs on the project repository

### Performance Optimization

#### 1. Caching

```python
# Enable caching for repeated analysis
from stockpal.core.cache import CacheManager

cache = CacheManager()
cache.enable_caching(ttl_hours=24)
```

#### 2. Batch Processing

```python
# Process multiple symbols efficiently
symbols = ["VCB", "VIC", "VHM", "HPG"]
results = {}

for symbol in symbols:
    scraper = DataScraper(symbol)
    prices = scraper.fetch_prices()
    predictor = TrendPredictor(symbol, prices)
    results[symbol] = predictor.analyze_trend()
```

## Contributing

### Contribution Guidelines

1. **Fork** the repository
2. **Create** a feature branch
3. **Write** tests for new functionality
4. **Follow** code style guidelines
5. **Submit** a pull request

### Code Review Process

1. All code must pass automated tests
2. Code coverage should be maintained above 80%
3. Documentation must be updated for new features
4. Performance impact should be considered

### Release Process

1. **Version Bump**: Update version numbers
2. **Changelog**: Document all changes
3. **Testing**: Run full test suite
4. **Documentation**: Update user guides
5. **Release**: Create tagged release
