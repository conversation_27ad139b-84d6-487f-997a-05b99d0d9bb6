"""
Analysis service for orchestrating stock analysis operations.

This service provides a high-level interface for performing comprehensive
stock analysis, coordinating between various analyzers and processors.
"""

import logging
from typing import List, Optional
from datetime import datetime

from shared.models.stock_models import (
    StockAnalysis, AnalysisRequest, PricePoint, TechnicalIndicator,
    TrendAnalysis, TrendDirection, SignalType, ConfidenceLevel, MarketCondition,
    TradingZone, RiskRewardRatio
)
from shared.exceptions.stock_exceptions import (
    AnalysisException, InsufficientDataException, SymbolNotFoundException
)
from shared.utils.validation import validate_symbol
from core.processors.indicator_processor import IndicatorProcessor
from core.services.data_service import DataService
from core.services.signal_synthesizer import SignalSynthesizer


logger = logging.getLogger(__name__)


class AnalysisService:
    """Service for performing comprehensive stock analysis."""

    def __init__(
        self,
        data_service: DataService,
        indicator_processor: IndicatorProcessor
    ):
        """
        Initialize the analysis service.

        Args:
            data_service: Service for data operations
            indicator_processor: Processor for technical indicators
        """
        self._data_service = data_service
        self._indicator_processor = indicator_processor
        self._signal_synthesizer = SignalSynthesizer()
        self._logger = logging.getLogger(__name__)

    def analyze_stock(self, request: AnalysisRequest) -> StockAnalysis:
        """
        Perform comprehensive stock analysis.

        Args:
            request: Analysis request with parameters

        Returns:
            Complete stock analysis result

        Raises:
            AnalysisException: If analysis fails
            SymbolNotFoundException: If symbol is not found
            InsufficientDataException: If not enough data is available
        """
        try:
            symbol = validate_symbol(request.symbol)

            self._logger.info(f"Starting analysis for {symbol}")

            # Get price data
            prices = self._data_service.get_daily_prices(
                symbol=symbol,
                days=request.days_back,
                end_date=request.end_date
            )

            if len(prices) < 30:  # Minimum data requirement
                raise InsufficientDataException(
                    f"Insufficient data for analysis: {len(prices)} days (minimum 30 required)"
                )

            # Sort prices by timestamp (oldest first for calculations)
            prices.sort(key=lambda x: x.timestamp)

            # Calculate technical indicators
            technical_indicators = self._calculate_technical_indicators(symbol, prices, request.indicators)

            # Perform trend analysis
            trend_analysis = self._analyze_trend(prices, technical_indicators)

            # Calculate price changes
            current_price = prices[-1].close_price
            price_change = prices[-1].change_price or 0.0
            price_change_percent = prices[-1].change_percent or 0.0

            # Use SignalSynthesizer for comprehensive analysis
            synthesis_result = self._signal_synthesizer.synthesize_analysis(
                symbol=symbol,
                prices=prices,
                technical_indicators=technical_indicators,
                trend_analysis=trend_analysis,
                include_zones=request.include_zones,
                include_risk_analysis=request.include_risk_analysis
            )

            buy_zones = synthesis_result.get("buy_zones", [])
            stop_loss_zones = synthesis_result.get("stop_loss_zones", [])
            take_profit_zones = synthesis_result.get("take_profit_zones", [])
            risk_reward_ratios = synthesis_result.get("risk_reward_ratios", [])

            # Get recommendation from synthesizer or fallback to basic logic
            recommendation = synthesis_result.get("recommendation", "HOLD")
            if isinstance(recommendation, str):
                # Convert string recommendation to SignalType
                rec_mapping = {
                    "STRONG_BUY": SignalType.BUY,
                    "BUY": SignalType.BUY,
                    "HOLD": SignalType.NEUTRAL,
                    "SELL": SignalType.SELL,
                    "STRONG_SELL": SignalType.SELL
                }
                recommendation = rec_mapping.get(recommendation, SignalType.NEUTRAL)

            # Get market condition from synthesizer or fallback
            market_condition = synthesis_result.get("market_condition", MarketCondition.NEUTRAL)

            # Generate technical summary from synthesizer or fallback
            technical_summary = synthesis_result.get("technical_summary",
                self._generate_technical_summary(symbol, prices, technical_indicators, trend_analysis)
            )

            # Calculate confidence score
            confidence_score = self._calculate_confidence_score(technical_indicators, trend_analysis)

            # Create analysis result
            analysis = StockAnalysis(
                symbol=symbol,
                current_price=current_price,
                analysis_date=datetime.now(),
                last_trading_date=datetime.fromtimestamp(prices[-1].timestamp),
                trend_analysis=trend_analysis,
                price_change=price_change,
                price_change_percent=price_change_percent,
                technical_indicators=technical_indicators,
                buy_zones=buy_zones,
                stop_loss_zones=stop_loss_zones,
                take_profit_zones=take_profit_zones,
                risk_reward_ratios=risk_reward_ratios,
                recommendation=recommendation,
                market_condition=market_condition,
                technical_summary=technical_summary,
                confidence_score=confidence_score
            )

            self._logger.info(f"Completed analysis for {symbol}")
            return analysis

        except Exception as e:
            if isinstance(e, (SymbolNotFoundException, InsufficientDataException)):
                raise

            error_msg = f"Failed to analyze stock {request.symbol}: {str(e)}"
            self._logger.error(error_msg, exc_info=True)
            raise AnalysisException(error_msg)

    def _calculate_technical_indicators(
        self,
        symbol: str,
        prices: List[PricePoint],
        indicator_configs: Optional[List] = None
    ) -> List[TechnicalIndicator]:
        """Calculate technical indicators for the stock."""
        try:
            indicators = []
            current_timestamp = prices[-1].timestamp

            # Moving Averages
            ma_result = self._indicator_processor.calculate_moving_averages(symbol, prices)
            for period in [5, 10, 20, 50, 100, 200]:
                for ma_type in ["sma", "ema"]:
                    key = f"{ma_type}{period}"
                    if key in ma_result and ma_result[key]:
                        value = ma_result[key][-1] if ma_result[key] else None
                        if value is not None:
                            signal = self._determine_ma_signal(prices[-1].close_price, value)
                            indicators.append(TechnicalIndicator(
                                name=f"{ma_type.upper()}{period}",
                                value=value,
                                signal=signal,
                                confidence=ConfidenceLevel.MEDIUM,
                                description=f"{ma_type.upper()} {period} periods",
                                timestamp=current_timestamp
                            ))

            # MACD
            macd_result = self._indicator_processor.calculate_macd(symbol, prices)
            if macd_result.get("macd_line") and macd_result.get("signal_line"):
                macd_line = macd_result["macd_line"][-1]
                signal_line = macd_result["signal_line"][-1]
                if macd_line is not None and signal_line is not None:
                    signal = SignalType.BUY if macd_line > signal_line else SignalType.SELL
                    indicators.append(TechnicalIndicator(
                        name="MACD",
                        value=macd_line,
                        signal=signal,
                        confidence=ConfidenceLevel.HIGH,
                        description="MACD Line vs Signal Line",
                        timestamp=current_timestamp
                    ))

            # RSI
            rsi_values = self._indicator_processor.calculate_rsi(symbol, prices)
            if rsi_values:
                rsi_value = rsi_values[-1]
                if rsi_value is not None:
                    signal = self._determine_rsi_signal(rsi_value)
                    indicators.append(TechnicalIndicator(
                        name="RSI",
                        value=rsi_value,
                        signal=signal,
                        confidence=ConfidenceLevel.HIGH,
                        description="Relative Strength Index (14)",
                        timestamp=current_timestamp
                    ))

            # Bollinger Bands
            bb_result = self._indicator_processor.calculate_bollinger_bands(symbol, prices)
            if all(key in bb_result and bb_result[key] for key in ["upper_band", "lower_band"]):
                upper_band = bb_result["upper_band"][-1]
                lower_band = bb_result["lower_band"][-1]
                current_price = prices[-1].close_price

                if upper_band is not None and lower_band is not None:
                    signal = self._determine_bb_signal(current_price, upper_band, lower_band)
                    indicators.append(TechnicalIndicator(
                        name="Bollinger Bands",
                        value=current_price,
                        signal=signal,
                        confidence=ConfidenceLevel.MEDIUM,
                        description="Price position relative to Bollinger Bands",
                        timestamp=current_timestamp
                    ))

            # Single value indicators
            single_indicators = [
                ("CCI", "Commodity Channel Index"),
                ("WPR", "Williams %R"),
                ("ROC", "Rate of Change"),
                ("SAR", "Parabolic SAR"),
                ("OBV", "On Balance Volume"),
                ("AO", "Awesome Oscillator"),
                ("ADI", "Accumulation Distribution Index"),
                ("Momentum", "Momentum"),
                ("BearPower", "Bear Power"),
                ("ULTOSC", "Ultimate Oscillator")
            ]

            for indicator_name, description in single_indicators:
                try:
                    value = self._indicator_processor.calculate_single_value_indicator(
                        symbol, prices, indicator_name.lower()
                    )
                    if value is not None:
                        signal = self._determine_generic_signal(indicator_name, value)
                        indicators.append(TechnicalIndicator(
                            name=indicator_name,
                            value=value,
                            signal=signal,
                            confidence=ConfidenceLevel.MEDIUM,
                            description=description,
                            timestamp=current_timestamp
                        ))
                except Exception as e:
                    self._logger.warning(f"Failed to calculate {indicator_name}: {str(e)}")

            return indicators

        except Exception as e:
            self._logger.error(f"Failed to calculate technical indicators: {str(e)}")
            return []

    def _analyze_trend(self, prices: List[PricePoint], indicators: List[TechnicalIndicator]) -> TrendAnalysis:
        """Analyze the overall trend of the stock."""
        try:
            # Simple trend analysis based on price movement and moving averages
            current_price = prices[-1].close_price

            # Get SMA20 and SMA50 for trend analysis
            sma20_indicator = next((ind for ind in indicators if ind.name == "SMA20"), None)
            sma50_indicator = next((ind for ind in indicators if ind.name == "SMA50"), None)

            # Determine trend direction
            direction = TrendDirection.SIDEWAYS
            strength = 0.5
            confidence = ConfidenceLevel.MEDIUM

            if sma20_indicator and sma50_indicator:
                sma20 = sma20_indicator.value
                sma50 = sma50_indicator.value

                if current_price > sma20 > sma50:
                    direction = TrendDirection.BULLISH
                    strength = 0.8
                    confidence = ConfidenceLevel.HIGH
                elif current_price < sma20 < sma50:
                    direction = TrendDirection.BEARISH
                    strength = 0.8
                    confidence = ConfidenceLevel.HIGH
                elif current_price > sma20 and sma20 < sma50:
                    direction = TrendDirection.BULLISH
                    strength = 0.6
                    confidence = ConfidenceLevel.MEDIUM
                elif current_price < sma20 and sma20 > sma50:
                    direction = TrendDirection.BEARISH
                    strength = 0.6
                    confidence = ConfidenceLevel.MEDIUM

            # Calculate trend duration (simplified)
            duration_days = None
            if len(prices) >= 20:
                # Look for trend consistency over the last 20 days
                recent_prices = prices[-20:]
                price_changes = [
                    recent_prices[i].close_price - recent_prices[i-1].close_price
                    for i in range(1, len(recent_prices))
                ]

                positive_days = sum(1 for change in price_changes if change > 0)
                if positive_days >= 14:  # 70% positive days
                    duration_days = positive_days
                elif positive_days <= 6:  # 30% positive days
                    duration_days = len(price_changes) - positive_days

            return TrendAnalysis(
                direction=direction,
                strength=strength,
                confidence=confidence,
                duration_days=duration_days
            )

        except Exception as e:
            self._logger.error(f"Failed to analyze trend: {str(e)}")
            return TrendAnalysis(
                direction=TrendDirection.UNKNOWN,
                strength=0.0,
                confidence=ConfidenceLevel.LOW
            )

    def _determine_ma_signal(self, current_price: float, ma_value: float) -> SignalType:
        """Determine signal based on moving average."""
        if current_price > ma_value:
            return SignalType.BUY
        elif current_price < ma_value:
            return SignalType.SELL
        else:
            return SignalType.NEUTRAL

    def _determine_rsi_signal(self, rsi_value: float) -> SignalType:
        """Determine signal based on RSI value."""
        if rsi_value < 30:
            return SignalType.BUY  # Oversold
        elif rsi_value > 70:
            return SignalType.SELL  # Overbought
        else:
            return SignalType.NEUTRAL

    def _determine_bb_signal(self, price: float, upper_band: float, lower_band: float) -> SignalType:
        """Determine signal based on Bollinger Bands."""
        if price <= lower_band:
            return SignalType.BUY  # Price at lower band
        elif price >= upper_band:
            return SignalType.SELL  # Price at upper band
        else:
            return SignalType.NEUTRAL

    def _determine_generic_signal(self, indicator_name: str, value: float) -> SignalType:
        """Determine signal for generic indicators."""
        # Simplified signal determination - can be enhanced with more sophisticated logic
        if indicator_name.upper() in ["CCI"]:
            if value < -100:
                return SignalType.BUY
            elif value > 100:
                return SignalType.SELL
        elif indicator_name.upper() in ["WPR"]:
            if value < -80:
                return SignalType.BUY
            elif value > -20:
                return SignalType.SELL
        elif indicator_name.upper() in ["ROC", "MOMENTUM"]:
            if value > 0:
                return SignalType.BUY
            elif value < 0:
                return SignalType.SELL

        return SignalType.NEUTRAL

    def _calculate_trading_zones(
        self,
        symbol: str,
        prices: List[PricePoint],
        indicators: List[TechnicalIndicator]
    ) -> dict:
        """Calculate trading zones (buy, stop-loss, take-profit)."""
        try:
            current_price = prices[-1].close_price

            # Simple zone calculation based on support/resistance and moving averages
            buy_zones = []
            stop_loss_zones = []
            take_profit_zones = []

            # Get key moving averages
            sma20 = next((ind.value for ind in indicators if ind.name == "SMA20"), None)
            sma50 = next((ind.value for ind in indicators if ind.name == "SMA50"), None)

            # Calculate recent support and resistance levels
            recent_prices = prices[-20:] if len(prices) >= 20 else prices
            highs = [p.high_price for p in recent_prices]
            lows = [p.low_price for p in recent_prices]

            resistance_level = max(highs)
            support_level = min(lows)

            # Buy zones (support levels and below moving averages)
            if sma20 and sma20 < current_price:
                buy_zones.append(TradingZone(
                    price=sma20,
                    confidence=ConfidenceLevel.MEDIUM,
                    reason="SMA20 support level",
                    zone_type="buy"
                ))

            if support_level < current_price * 0.95:  # At least 5% below current price
                buy_zones.append(TradingZone(
                    price=support_level,
                    confidence=ConfidenceLevel.HIGH,
                    reason="Recent support level",
                    zone_type="buy"
                ))

            # Stop-loss zones (below support levels)
            if support_level > 0:
                stop_loss_zones.append(TradingZone(
                    price=support_level * 0.95,  # 5% below support
                    confidence=ConfidenceLevel.HIGH,
                    reason="Below support level",
                    zone_type="stop_loss"
                ))

            # Take-profit zones (resistance levels)
            if resistance_level > current_price * 1.02:  # At least 2% above current price
                take_profit_zones.append(TradingZone(
                    price=resistance_level,
                    confidence=ConfidenceLevel.HIGH,
                    reason="Recent resistance level",
                    zone_type="take_profit"
                ))

            # Additional take-profit based on risk-reward ratio
            if buy_zones and stop_loss_zones:
                avg_buy_price = sum(zone.price for zone in buy_zones) / len(buy_zones)
                avg_stop_loss = sum(zone.price for zone in stop_loss_zones) / len(stop_loss_zones)
                risk = avg_buy_price - avg_stop_loss

                # 2:1 risk-reward ratio
                take_profit_price = avg_buy_price + (risk * 2)
                if take_profit_price > current_price:
                    take_profit_zones.append(TradingZone(
                        price=take_profit_price,
                        confidence=ConfidenceLevel.MEDIUM,
                        reason="2:1 risk-reward ratio",
                        zone_type="take_profit"
                    ))

            return {
                "buy_zones": buy_zones,
                "stop_loss_zones": stop_loss_zones,
                "take_profit_zones": take_profit_zones
            }

        except Exception as e:
            self._logger.error(f"Failed to calculate trading zones: {str(e)}")
            return {"buy_zones": [], "stop_loss_zones": [], "take_profit_zones": []}

    def _calculate_risk_reward_ratios(self, buy_zones, stop_loss_zones, take_profit_zones) -> list:
        """Calculate risk-reward ratios for trading scenarios."""
        try:
            ratios = []

            for buy_zone in buy_zones[:3]:  # Limit to top 3 buy zones
                buy_price = buy_zone.price

                for stop_loss_zone in stop_loss_zones:
                    stop_loss_price = stop_loss_zone.price

                    if stop_loss_price >= buy_price:
                        continue  # Invalid: stop loss should be below buy price

                    for take_profit_zone in take_profit_zones:
                        take_profit_price = take_profit_zone.price

                        if take_profit_price <= buy_price:
                            continue  # Invalid: take profit should be above buy price

                        risk = buy_price - stop_loss_price
                        reward = take_profit_price - buy_price

                        if risk > 0:
                            ratio = reward / risk

                            # Determine quality
                            if ratio >= 3:
                                quality = "Excellent"
                            elif ratio >= 2:
                                quality = "Good"
                            elif ratio >= 1.5:
                                quality = "Fair"
                            elif ratio >= 1:
                                quality = "Poor"
                            else:
                                quality = "Very Poor"

                            ratios.append(RiskRewardRatio(
                                buy_price=buy_price,
                                stop_loss_price=stop_loss_price,
                                take_profit_price=take_profit_price,
                                ratio=ratio,
                                quality=quality
                            ))

            # Sort by ratio (best first) and return top 5
            ratios.sort(key=lambda x: x.ratio, reverse=True)
            return ratios[:5]

        except Exception as e:
            self._logger.error(f"Failed to calculate risk-reward ratios: {str(e)}")
            return []

    def _generate_recommendation(self, indicators: List[TechnicalIndicator], trend: TrendAnalysis) -> SignalType:
        """Generate overall recommendation based on indicators and trend."""
        try:
            # Count signals
            buy_signals = sum(1 for ind in indicators if ind.signal == SignalType.BUY)
            sell_signals = sum(1 for ind in indicators if ind.signal == SignalType.SELL)
            total_signals = buy_signals + sell_signals

            if total_signals == 0:
                return SignalType.NEUTRAL

            # Weight by trend analysis
            trend_weight = 2 if trend.confidence in [ConfidenceLevel.HIGH, ConfidenceLevel.VERY_HIGH] else 1

            if trend.direction == TrendDirection.BULLISH:
                buy_signals += trend_weight
            elif trend.direction == TrendDirection.BEARISH:
                sell_signals += trend_weight

            # Determine recommendation
            total_weighted = buy_signals + sell_signals
            buy_ratio = buy_signals / total_weighted if total_weighted > 0 else 0

            if buy_ratio >= 0.6:
                return SignalType.BUY
            elif buy_ratio <= 0.4:
                return SignalType.SELL
            else:
                return SignalType.HOLD

        except Exception as e:
            self._logger.error(f"Failed to generate recommendation: {str(e)}")
            return SignalType.NEUTRAL

    def _assess_market_condition(self, prices: List[PricePoint], indicators: List[TechnicalIndicator]) -> MarketCondition:
        """Assess overall market condition."""
        try:
            # Calculate volatility
            recent_prices = prices[-20:] if len(prices) >= 20 else prices
            price_changes = [
                abs(recent_prices[i].close_price - recent_prices[i-1].close_price) / recent_prices[i-1].close_price
                for i in range(1, len(recent_prices))
            ]
            volatility = sum(price_changes) / len(price_changes) if price_changes else 0

            # Assess volume trend
            volumes = [p.volume for p in recent_prices]
            avg_volume = sum(volumes) / len(volumes) if volumes else 0
            recent_volume = volumes[-1] if volumes else 0

            if recent_volume > avg_volume * 1.5:
                volume_trend = "High"
            elif recent_volume < avg_volume * 0.5:
                volume_trend = "Low"
            else:
                volume_trend = "Normal"

            # Determine overall condition
            rsi_indicator = next((ind for ind in indicators if ind.name == "RSI"), None)

            condition = "Neutral"
            if rsi_indicator:
                if rsi_indicator.value > 70:
                    condition = "Overbought"
                elif rsi_indicator.value < 30:
                    condition = "Oversold"

            if volatility > 0.05:  # 5% daily volatility
                condition = "Volatile"

            return MarketCondition(
                condition=condition,
                volatility=volatility,
                volume_trend=volume_trend
            )

        except Exception as e:
            self._logger.error(f"Failed to assess market condition: {str(e)}")
            return MarketCondition(condition="Unknown", volatility=0.0, volume_trend="Unknown")

    def _generate_technical_summary(
        self,
        symbol: str,
        prices: List[PricePoint],
        indicators: List[TechnicalIndicator],
        trend: TrendAnalysis
    ) -> str:
        """Generate a technical summary of the analysis."""
        try:
            current_price = prices[-1].close_price
            price_change = prices[-1].change_percent or 0.0

            summary_parts = [
                f"=== TECHNICAL ANALYSIS SUMMARY FOR {symbol} ===",
                f"Current Price: {current_price:,.0f} VND ({price_change:+.2f}%)",
                f"Trend Direction: {trend.direction.value.title()}",
                f"Trend Strength: {trend.strength:.1%}",
                f"Analysis Confidence: {trend.confidence.value.replace('_', ' ').title()}"
            ]

            # Add key indicator insights
            rsi_indicator = next((ind for ind in indicators if ind.name == "RSI"), None)
            if rsi_indicator:
                summary_parts.append(f"RSI (14): {rsi_indicator.value:.1f} - {rsi_indicator.signal.value.title()}")

            macd_indicator = next((ind for ind in indicators if ind.name == "MACD"), None)
            if macd_indicator:
                summary_parts.append(f"MACD: {macd_indicator.signal.value.title()} signal")

            # Add moving average analysis
            sma20 = next((ind for ind in indicators if ind.name == "SMA20"), None)
            sma50 = next((ind for ind in indicators if ind.name == "SMA50"), None)

            if sma20 and sma50:
                if current_price > sma20.value > sma50.value:
                    summary_parts.append("Price above both SMA20 and SMA50 - Strong bullish trend")
                elif current_price < sma20.value < sma50.value:
                    summary_parts.append("Price below both SMA20 and SMA50 - Strong bearish trend")
                else:
                    summary_parts.append("Mixed signals from moving averages")

            return "\n".join(summary_parts)

        except Exception as e:
            self._logger.error(f"Failed to generate technical summary: {str(e)}")
            return f"Technical analysis summary for {symbol} - Analysis completed with limited data"

    def _calculate_confidence_score(self, indicators: List[TechnicalIndicator], trend: TrendAnalysis) -> float:
        """Calculate overall confidence score for the analysis."""
        try:
            if not indicators:
                return 0.0

            # Base confidence from indicators
            confidence_scores = []
            for indicator in indicators:
                if indicator.confidence == ConfidenceLevel.VERY_HIGH:
                    confidence_scores.append(0.9)
                elif indicator.confidence == ConfidenceLevel.HIGH:
                    confidence_scores.append(0.8)
                elif indicator.confidence == ConfidenceLevel.MEDIUM:
                    confidence_scores.append(0.6)
                elif indicator.confidence == ConfidenceLevel.LOW:
                    confidence_scores.append(0.4)
                else:
                    confidence_scores.append(0.2)

            avg_indicator_confidence = sum(confidence_scores) / len(confidence_scores)

            # Trend confidence
            if trend.confidence == ConfidenceLevel.VERY_HIGH:
                trend_confidence = 0.9
            elif trend.confidence == ConfidenceLevel.HIGH:
                trend_confidence = 0.8
            elif trend.confidence == ConfidenceLevel.MEDIUM:
                trend_confidence = 0.6
            elif trend.confidence == ConfidenceLevel.LOW:
                trend_confidence = 0.4
            else:
                trend_confidence = 0.2

            # Weighted average (70% indicators, 30% trend)
            overall_confidence = (avg_indicator_confidence * 0.7) + (trend_confidence * 0.3)

            return round(overall_confidence * 100, 1)  # Return as percentage

        except Exception as e:
            self._logger.error(f"Failed to calculate confidence score: {str(e)}")
            return 50.0  # Default moderate confidence